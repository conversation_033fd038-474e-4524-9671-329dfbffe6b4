"""
使用Selenium处理需要JavaScript的网站
需要安装: pip install selenium webdriver-manager
"""

import time
import random
from concurrent.futures import ThreadPoolExecutor, as_completed
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
import trafilatura
from bs4 import BeautifulSoup

def create_driver(headless=True, proxy=None):
    """创建Chrome WebDriver"""
    options = Options()
    
    if headless:
        options.add_argument('--headless')
    
    # 基本设置
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-gpu')
    options.add_argument('--window-size=1920,1080')
    
    # 反检测设置
    options.add_argument('--disable-blink-features=AutomationControlled')
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)
    
    # 设置User-Agent
    options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    # 代理设置
    if proxy:
        options.add_argument(f'--proxy-server={proxy}')
    
    try:
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
        
        # 执行反检测脚本
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        return driver
    except Exception as e:
        print(f"创建WebDriver失败: {e}")
        return None

def extract_text_with_selenium(url, wait_time=10, proxy=None):
    """使用Selenium提取需要JavaScript的网页内容"""
    driver = None
    try:
        # 设置代理
        proxy_url = "http://nbproxy.mlp.oppo.local:8888" if proxy is None else proxy
        
        driver = create_driver(headless=True, proxy=proxy_url)
        if not driver:
            return None
        
        print(f"使用Selenium访问: {url}")
        driver.get(url)
        
        # 等待页面加载
        time.sleep(random.uniform(2, 4))
        
        # 等待特定元素加载（可根据网站调整）
        try:
            WebDriverWait(driver, wait_time).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
        except TimeoutException:
            print("页面加载超时")
        
        # 滚动页面以触发懒加载
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight/2);")
        time.sleep(1)
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(1)
        driver.execute_script("window.scrollTo(0, 0);")
        time.sleep(1)
        
        # 获取页面源码
        html_content = driver.page_source
        
        # 使用trafilatura提取内容
        result = trafilatura.extract(html_content)
        
        if result:
            print(f"Selenium成功提取内容，长度: {len(result)} 字符")
            return result
        else:
            # 备用方案：使用BeautifulSoup
            print("trafilatura提取失败，使用BeautifulSoup...")
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 移除脚本和样式
            for element in soup(['script', 'style', 'nav', 'footer', 'header']):
                element.decompose()
            
            text = soup.get_text(separator='\n', strip=True)
            return text
            
    except WebDriverException as e:
        print(f"WebDriver错误: {e}")
        return None
    except Exception as e:
        print(f"未知错误: {e}")
        return None
    finally:
        if driver:
            driver.quit()

def extract_text_hybrid(url, use_selenium_fallback=True):
    """
    混合方案：先尝试requests，失败后使用selenium
    """
    from web_utils import extract_text

    print(f"首先尝试requests方式访问: {url}")
    result = extract_text(url, max_retries=3, delay_range=(1, 3))

    if result:
        return result

    if use_selenium_fallback:
        print(f"requests失败，尝试Selenium方式...")
        return extract_text_with_selenium(url)

    return None

def extract_text_hybrid_single(url, use_selenium_fallback=True, thread_id=None):
    """
    单个URL的混合提取方案（用于并发调用）
    """
    thread_prefix = f"[线程{thread_id}] " if thread_id else ""

    try:
        from web_utils import extract_text

        print(f"{thread_prefix}开始处理: {url}")

        # 先尝试requests方式
        result = extract_text(url, max_retries=2, delay_range=(0.5, 1.5))

        if result:
            print(f"{thread_prefix}✅ requests成功: {url} ({len(result)} 字符)")
            return {
                'url': url,
                'content': result,
                'method': 'requests',
                'success': True,
                'error': None
            }

        # requests失败，尝试selenium
        if use_selenium_fallback:
            print(f"{thread_prefix}requests失败，尝试Selenium: {url}")
            result = extract_text_with_selenium(url)

            if result:
                print(f"{thread_prefix}✅ Selenium成功: {url} ({len(result)} 字符)")
                return {
                    'url': url,
                    'content': result,
                    'method': 'selenium',
                    'success': True,
                    'error': None
                }

        print(f"{thread_prefix}❌ 所有方法都失败: {url}")
        return {
            'url': url,
            'content': None,
            'method': None,
            'success': False,
            'error': 'All methods failed'
        }

    except Exception as e:
        print(f"{thread_prefix}❌ 异常错误: {url} - {e}")
        return {
            'url': url,
            'content': None,
            'method': None,
            'success': False,
            'error': str(e)
        }

def extract_text_concurrent(urls: list[str], max_workers=3, use_selenium_fallback=True,
                          progress_callback=None, delay_between_batches=1.0):
    """
    并发提取多个URL的文本内容

    Args:
        urls: URL列表
        max_workers: 最大并发数量 (默认3，避免过多并发导致被封)
        use_selenium_fallback: 是否使用selenium作为备用方案
        progress_callback: 进度回调函数，接收 (completed, total, current_result) 参数
        delay_between_batches: 批次间延迟时间(秒)

    Returns:
        list: 包含每个URL结果的字典列表
        [
            {
                'url': 'https://example.com',
                'content': '提取的内容' or None,
                'method': 'requests' or 'selenium' or None,
                'success': True or False,
                'error': None or '错误信息'
            },
            ...
        ]
    """
    if not urls:
        return []

    print(f"🚀 开始并发提取 {len(urls)} 个URL，并发数: {max_workers}")

    results = []
    completed_count = 0

    # 使用线程池执行并发请求
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_url = {
            executor.submit(
                extract_text_hybrid_single,
                url,
                use_selenium_fallback,
                i+1
            ): url
            for i, url in enumerate(urls)
        }

        # 收集结果
        for future in as_completed(future_to_url):
            url = future_to_url[future]
            completed_count += 1

            try:
                result = future.result()
                results.append(result)

                # 调用进度回调
                if progress_callback:
                    progress_callback(completed_count, len(urls), result)

                print(f"📊 进度: {completed_count}/{len(urls)} - {result['url']}")

                # 添加延迟避免请求过于频繁
                if completed_count < len(urls) and delay_between_batches > 0:
                    time.sleep(delay_between_batches)

            except Exception as e:
                error_result = {
                    'url': url,
                    'content': None,
                    'method': None,
                    'success': False,
                    'error': f'Future exception: {e}'
                }
                results.append(error_result)

                if progress_callback:
                    progress_callback(completed_count, len(urls), error_result)

                print(f"❌ 任务异常: {url} - {e}")

    # 按原始URL顺序排序结果
    url_to_index = {url: i for i, url in enumerate(urls)}
    results.sort(key=lambda x: url_to_index.get(x['url'], float('inf')))

    # 统计结果
    success_count = sum(1 for r in results if r['success'])
    requests_count = sum(1 for r in results if r['method'] == 'requests')
    selenium_count = sum(1 for r in results if r['method'] == 'selenium')

    print(f"\n📈 并发提取完成:")
    print(f"   总计: {len(results)} 个URL")
    print(f"   成功: {success_count} 个 ({success_count/len(results)*100:.1f}%)")
    print(f"   失败: {len(results)-success_count} 个")
    print(f"   方法: requests={requests_count}, selenium={selenium_count}")

    return results


# ====== 示例 =======


def progress_callback_example(completed, total, result):
    """进度回调示例函数"""
    status = "✅" if result['success'] else "❌"
    method = f"({result['method']})" if result['method'] else ""
    print(f"  {status} {completed}/{total}: {result['url']} {method}")

def test_concurrent_extraction():
    """测试并发提取功能"""
    test_urls = [
        'https://en.wikipedia.org/wiki/Greg_Rucka',
        'https://en.wikipedia.org/wiki/Batman',
        'https://www.bookbrowse.com/biographies/index.cfm/author_number/1687/Greg-Rucka',
        'https://m.imdb.com/name/nm1421638/',
        'https://www.bookseriesinorder.com/greg-rucka/',
        'https://en.wikipedia.org/wiki/Wonder_Woman',
    ]

    print("🧪 测试并发提取功能")
    print("="*60)

    # 测试并发提取
    results = extract_text_concurrent(
        urls=test_urls,
        max_workers=3,  # 3个并发
        use_selenium_fallback=True,
        progress_callback=progress_callback_example,
        delay_between_batches=0.5
    )

    print("\n📋 详细结果:")
    print("="*60)

    for i, result in enumerate(results, 1):
        print(f"\n{i}. {result['url']}")
        print(f"   状态: {'✅ 成功' if result['success'] else '❌ 失败'}")
        if result['method']:
            print(f"   方法: {result['method']}")
        if result['error']:
            print(f"   错误: {result['error']}")
        if result['content']:
            content_preview = result['content'][:200] + "..." if len(result['content']) > 200 else result['content']
            print(f"   内容预览: {content_preview}")

    return results

if __name__ == '__main__':
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == 'test_concurrent':
        # 测试并发功能
        test_concurrent_extraction()
    else:
        # 原有的单个测试
        test_urls = [
            'https://www.bookbrowse.com/biographies/index.cfm/author_number/1687/Greg-Rucka',
            'https://en.wikipedia.org/wiki/Greg_Rucka',
        ]

        print("🧪 测试单个URL提取")
        for url in test_urls:
            print('\n' + '='*60)
            print(f"测试URL: {url}")
            print('='*60)

            result = extract_text_hybrid(url)

            if result:
                print(f"✅ 成功提取内容 (前500字符):")
                print(result[:500] + "..." if len(result) > 500 else result)
            else:
                print("❌ 提取失败")

            # 添加延迟
            time.sleep(random.uniform(3, 6))
