# SRR1 优化项目

本项目基于[ReCall re-search分支](https://github.com/Agent-RL/ReCall/tree/re-search)进行优化改进，提出SRR1方法，通过检索过程优化和奖励机制改进增强模型推理能力。

## SRR1 方法改进

### 核心优化点
1. **推理过程优化**
   - 在问题推理搜索阶段添加**文本压缩精炼**处理
   - 增加**生成次数限制**防止无限循环

2. **奖励函数增强**
   - 新增检索次数统计奖励项
   - 增加判断逻辑：验证ground truth是否出现在
     - 最后检索文档（last_document）
     - 最后精炼内容（last_refine）

## 代码修改说明

### 训练相关修改
| 文件路径 | 修改内容 |
|---------|---------|
| `src/verl/trainer/ppo/ray_trainer.py` | 在`compute_data_metrics`添加检索次数统计逻辑 |
| `src/verl/workers/rollout/vllm_rollout.py` | <ul><li>新增生成次数限制逻辑</li><li>文档压缩处理模块</li><li>末文档Tensor化存储</li></ul> |
| `src/verl/workers/reward_manager/re_search.py` | 在`__call__`函数中集成检索次数和末文档转换逻辑 |
| `src/verl/utils/reward_score/__init__.py` | `_default_compute_score`新增检索次数和last_doc参数 |
| `src/verl/utils/reward_score/re_search.py` | 在`compute_score`中实现新奖励计算逻辑 |
| `src/verl/utils/dataset/template.py` | 更新系统提示模板 |

### 评测相关修改
| 文件路径 | 修改内容 |
|---------|---------|
| `scripts/evaluation/run_eval.py` | 新增SRR1策略定义和`max_retrieval_time`参数 |
| `src/flashrag/pipeline/active_pipeline.py` | 添加SRR1专用pipeline |

## 环境准备与运行

### 检索服务启动
```
# research-train环境

bash scripts/serving/retriever_serving.sh
```

### 模型训练流程
```
# research-train环境

# 1. 启动训练脚本（需提前配置好参数）
bash scripts/train/train_model.sh

# 2. 模型格式转换（训练完成后执行）
bash merge_select.sh  
```
### 评测服务部署
```
# 1. 启动SGL服务
bash scripts/serving/sgl.sh

# 2. 执行评测脚本
bash scripts/evaluation/eval.sh
```

