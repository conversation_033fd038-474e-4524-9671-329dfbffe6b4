import time
import requests
from typing import List

class BatchSearchDemo:
    def __init__(self, search_url: str = "http://10.237.35.107:8000"):
        self.search_url = search_url
    
    def batch_search(self, queries: List[str], top_n: int = 5) -> List[str]:
        """执行批量搜索并返回结果"""
        if not queries:
            return []
            
        url = f'{self.search_url}/batch_search'
        data = {'query': queries, 'top_n': top_n}
        
        start_time = time.time()
        response = requests.post(url, json=data)
        elapsed_time = time.time() - start_time
        
        results = [
            "\n\n".join(line['contents'] for line in item).strip()
            for item in response.json()
        ]
        
        return results, elapsed_time

def main():
    # 初始化搜索演示类
    searcher = BatchSearchDemo()
    
    # 定义测试查询
    test_queries = [
        'spouse of ernest heming<PERSON>',
        "Who was <PERSON>'s spouse?",
        'who was the spouse of <PERSON>?',
        "Who was <PERSON>'s spouse in the 1970s?",
        '<PERSON> spouse',
        'Who is the highest paid athlete in the world in 2023 and what is their job?',
        '<PERSON> job',
        "<PERSON>' primary job as a professional athlete",
        'What is the music video for the song "She Doesn\'t Mind" by <PERSON> <PERSON> about, and who are the models in it?',
        'lyricist of Break It Off song',
        "Artist of She Doesn't Mind",
        "model in the video for She Doesn't Mind by Rihanna featuring <PERSON> <PERSON>",
        "who is the model in Sean <PERSON> She Doesn't Mind music video?"
    ]
    
    print("开始批量搜索测试...")
    total_start = time.time()
    
    # 执行批量搜索
    results, total_time = searcher.batch_search(test_queries)
    
    # 打印汇总信息
    print(f"\n搜索完成，共处理 {len(test_queries)} 个查询")
    print(f"总耗时: {total_time:.2f}秒")
    print(f"平均每个查询耗时: {total_time/len(test_queries):.2f}秒\n")
    
    # 打印每个查询的详细结果
    for i, (query, result) in enumerate(zip(test_queries, results)):
        print(f"查询 {i+1}: {query}")
        print("-" * 50)
        print(result)
        print("\n" + "=" * 80 + "\n")

if __name__ == "__main__":
    main()
