import re
import time
import random
from bs4 import BeautifulSoup
import trafilatura
import requests
from fake_useragent import UserAgent
from llmlingua import PromptCompressor
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry


class ContentCompress:
    def __init__(self):

        # self.llm_lingua = PromptCompressor(
        #     model_name="microsoft/llmlingua-2-xlm-roberta-large-meetingbank",
        #     use_llmlingua2=True, # Whether to use llmlingua-2
        # )
        self.llm_lingua = PromptCompressor(
            # model_name="/home/<USER>/data/group/data_hub/huggingface/microsoft/llmlingua-2-bert-base-multilingual-cased-meetingbank",
            model_name="/home/<USER>/data/group/data_hub/huggingface/microsoft/llmlingua-2-xlm-roberta-large-meetingbank",
            use_llmlingua2=True, # Whether to use llmlingua-2
        )

    def compress(self, prompt, question='', ratio=0.33, max_token_num=500):
        token_len = self.llm_lingua.get_token_length(prompt)


        if token_len <= max_token_num:
            # 如果 token 长度小于等于 500，直接返回
            return prompt
        if token_len * ratio > max_token_num:
            # 如果 token 长度乘以压缩比例大于 500，压缩到 500
            target_token = max_token_num
            print("超 500 token len:", token_len)
        else:
            # 如果 token 长度乘以压缩比例小于 500，压缩到 token 长度乘以压缩比例
            target_token = token_len * ratio
            print("未超 500 token len:", token_len)
            
        compressed_prompt = self.llm_lingua.compress_prompt(
            prompt,
            question=question,
            # rate=ratio,
            target_token=target_token,
            force_tokens = [],
            chunk_end_tokens = [".", "。", "\n"],
            concate_question = False,
            use_context_level_filter = True if question else False,
            )
        return compressed_prompt

def clean_text(text):
    # Remove excess whitespace characters
    text = re.sub(r'\s+', ' ', text).strip()
    # Remove excess newline characters
    text = re.sub(r'\n+', '\n', text)
    return text

def extract_main_content(html):
    soup = BeautifulSoup(html, 'html.parser')
    
    # Remove scripts, styles, navigation, and footer elements
    for element in soup(['script', 'style', 'nav', 'footer', 'header']):
        element.decompose()
    
    # Try to find the main content area (assuming it uses <main> tag or id/class containing "content")
    main_content = soup.find('main') or soup.find(id=re.compile('content', re.I)) or soup.find(class_=re.compile('content', re.I))
    
    if main_content:
        text = main_content.get_text(separator='\n', strip=True)
    else:
        # If no clear main content area is found, use the content of <body>
        text = soup.body.get_text(separator='\n', strip=True)
    
    return clean_text(text)


def create_session_with_retry():
    """创建带有重试机制的session"""
    session = requests.Session()

    # 配置重试策略
    retry_strategy = Retry(
        total=3,  # 总重试次数
        backoff_factor=1,  # 重试间隔倍数
        status_forcelist=[429, 500, 502, 503, 504],  # 需要重试的状态码
    )

    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)

    return session

def get_enhanced_headers():
    """获取增强的请求头，模拟真实浏览器"""
    ua = UserAgent()
    return {
        'User-Agent': ua.random,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Cache-Control': 'max-age=0',
    }

def extract_text(url, max_retries=3, delay_range=(1, 3)):
    """
    提取网页文本内容，包含反爬虫保护

    Args:
        url: 目标URL
        max_retries: 最大重试次数
        delay_range: 请求间隔范围(秒)
    """
    proxies = {
        "http": "http://nbproxy.mlp.oppo.local:8888",
        "https": "http://nbproxy.mlp.oppo.local:8888"
    }

    session = create_session_with_retry()

    for attempt in range(max_retries):
        try:
            # 随机延迟，避免请求过于频繁
            if attempt > 0:
                delay = random.uniform(*delay_range)
                print(f"等待 {delay:.1f} 秒后重试...")
                time.sleep(delay)

            headers = get_enhanced_headers()

            print(f"尝试访问 {url} (第 {attempt + 1} 次)")

            response = session.get(
                url,
                proxies=proxies,
                headers=headers,
                timeout=30,  # 设置超时
                allow_redirects=True
            )

            # 检查响应状态
            if response.status_code == 429:
                print(f"遇到速率限制 (429)，等待更长时间...")
                if attempt < max_retries - 1:
                    time.sleep(random.uniform(5, 10))  # 更长的等待时间
                    continue
                else:
                    print(f"达到最大重试次数，跳过 {url}")
                    return None

            elif response.status_code == 403:
                print(f"访问被拒绝 (403)，可能需要JavaScript支持")
                return None

            elif response.status_code != 200:
                print(f"HTTP错误: {response.status_code}")
                if attempt < max_retries - 1:
                    continue
                else:
                    return None

            # 检查是否需要JavaScript
            if "Enable JavaScript" in response.text or "javascript" in response.text.lower():
                print(f"网站 {url} 需要JavaScript支持，无法通过简单HTTP请求访问")
                return None

            html_content = response.text
            result = trafilatura.extract(html_content)

            if result:
                print(f"成功提取内容，长度: {len(result)} 字符")
                return result
            else:
                print(f"trafilatura 无法提取内容，尝试BeautifulSoup...")
                # 备用方案：使用BeautifulSoup
                return extract_main_content(html_content)

        except requests.exceptions.RequestException as e:
            print(f"请求异常: {e}")
            if attempt < max_retries - 1:
                continue
            else:
                print(f"达到最大重试次数，跳过 {url}")
                return None
        except Exception as e:
            print(f"未知错误: {e}")
            return None

    return None


if __name__ == '__main__':
    compress = ContentCompress()

    urls = [
        'https://en.wikipedia.org/wiki/Greg_Rucka',
        'https://m.imdb.com/name/nm1421638/',
        # 'https://www.amazon.com/stores/author/B000AQ0860',
        'https://www.bookbrowse.com/biographies/index.cfm/author_number/1687/Greg-Rucka',
        'https://www.bookseriesinorder.com/greg-rucka/'
    ]

    for i, url in enumerate(urls):
        print('\n\n', '='*50)
        print(f"处理第 {i+1}/{len(urls)} 个URL: {url}")
        print('='*50)

        # 在每个URL之间添加延迟，避免请求过于频繁
        if i > 0:
            delay = random.uniform(2, 5)
            print(f"等待 {delay:.1f} 秒...")
            time.sleep(delay)

        out = extract_text_hybrid(url)

        if out:
            print(f"提取的内容 (前500字符):")
            print(out[:500] + "..." if len(out) > 500 else out)
            print('-'*30)

            try:
                compress_prompt = compress.compress(out)
                print(f"压缩后的内容:")
                print(compress_prompt)
            except Exception as e:
                print(f"压缩失败: {e}")
        else:
            print("❌ 无法提取内容")

        print('='*50)

#     prompt = """深夜，老旧的公寓里，三人围坐在昏黄的灯光下。林浩是一名警察，正在调查一起离奇失踪案。沈薇是失踪者的室友，神色紧张，手指不停搓动。还有一位神秘的老人刘伯，住在楼下，平时寡言少语。

# 林浩问沈薇：“案发当天你在哪里？”沈薇低头避开林浩的目光，支支吾吾地说：“我……我晚上出去买东西了。”林浩敏锐地捕捉到她的慌张。刘伯突然插话：“小薇那天回来的很晚，我听到她在楼道里哭。”沈薇猛地抬头，脸色苍白。

# 林浩追问沈薇为什么哭，沈薇终于崩溃：“我回家时，在门口看到一只奇怪的布偶，上面还沾着血迹。我害怕极了！”林浩询问刘伯是否见过那只布偶，刘伯犹豫片刻，说自己曾在垃圾桶里捡到过同样的东西，但已经烧掉了。

# 突然，林浩的手机响起，传来同事的消息：失踪者的指纹在刘伯家中发现。林浩目光一冷，整个房间陷入令人窒息的沉默……"""

#     compress = ContentCompress()
#     out = compress.compress(prompt * 5)
#     print(out)