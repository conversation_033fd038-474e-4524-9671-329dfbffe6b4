# ------------------------------------------------Environment Settings------------------------------------------------#
gpu_id: "0,1" #,2,3,4,5,6,7

# -------------------------------------------------Retrieval Settings------------------------------------------------#
# If set the name, the model path will be find in global paths
retrieval_method: "/home/<USER>/data/group/model_hub/huggingface/intfloat/e5-base-v2"  # name or path of the retrieval model. 
index_path: "/home/<USER>/data/group/data_hub/huggingface/RUC-NLPIR/FlashRAG_datasets/retrieval-corpus/e5_flat_inner.index" # path to the indexed file
faiss_gpu: True # whether use gpu to hold index
corpus_path: "/home/<USER>/data/group/data_hub/huggingface/RUC-NLPIR/FlashRAG_datasets/retrieval-corpus/wiki18_100w.jsonl"  # path to corpus in '.jsonl' format that store the documents