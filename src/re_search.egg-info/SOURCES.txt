LICENSE
README.md
setup.py
src/flashrag/__init__.py
src/flashrag/version.py
src/flashrag/config/__init__.py
src/flashrag/config/basic_config.yaml
src/flashrag/config/config.py
src/flashrag/dataset/__init__.py
src/flashrag/dataset/dataset.py
src/flashrag/dataset/utils.py
src/flashrag/evaluator/__init__.py
src/flashrag/evaluator/_bleu.py
src/flashrag/evaluator/evaluator.py
src/flashrag/evaluator/metrics.py
src/flashrag/evaluator/utils.py
src/flashrag/generator/__init__.py
src/flashrag/generator/fid.py
src/flashrag/generator/generator.py
src/flashrag/generator/multimodal_generator.py
src/flashrag/generator/openai_generator.py
src/flashrag/generator/stop_word_criteria.py
src/flashrag/generator/utils.py
src/flashrag/judger/__init__.py
src/flashrag/judger/judger.py
src/flashrag/pipeline/__init__.py
src/flashrag/pipeline/active_pipeline.py
src/flashrag/pipeline/branching_pipeline.py
src/flashrag/pipeline/mm_pipeline.py
src/flashrag/pipeline/pipeline.py
src/flashrag/pipeline/replug_utils.py
src/flashrag/prompt/__init__.py
src/flashrag/prompt/base_prompt.py
src/flashrag/prompt/mm_prompt.py
src/flashrag/prompt/selfask_examplars.py
src/flashrag/prompt/trace_examplars.py
src/flashrag/refiner/__init__.py
src/flashrag/refiner/kg_refiner.py
src/flashrag/refiner/llmlingua_compressor.py
src/flashrag/refiner/refiner.py
src/flashrag/refiner/selective_context_compressor.py
src/flashrag/retriever/__init__.py
src/flashrag/retriever/__main__.py
src/flashrag/retriever/encoder.py
src/flashrag/retriever/index_builder.py
src/flashrag/retriever/reranker.py
src/flashrag/retriever/retriever.py
src/flashrag/retriever/utils.py
src/flashrag/utils/__init__.py
src/flashrag/utils/constants.py
src/flashrag/utils/pred_parse.py
src/flashrag/utils/utils.py
src/re_search.egg-info/PKG-INFO
src/re_search.egg-info/SOURCES.txt
src/re_search.egg-info/dependency_links.txt
src/re_search.egg-info/requires.txt
src/re_search.egg-info/top_level.txt
src/verl/__init__.py
src/verl/protocol.py
src/verl/models/__init__.py
src/verl/models/registry.py
src/verl/models/weight_loader_registry.py
src/verl/models/llama/__init__.py
src/verl/models/llama/megatron/__init__.py
src/verl/models/llama/megatron/modeling_llama_megatron.py
src/verl/models/llama/megatron/checkpoint_utils/__init__.py
src/verl/models/llama/megatron/checkpoint_utils/llama_loader.py
src/verl/models/llama/megatron/checkpoint_utils/llama_saver.py
src/verl/models/llama/megatron/layers/__init__.py
src/verl/models/llama/megatron/layers/parallel_attention.py
src/verl/models/llama/megatron/layers/parallel_decoder.py
src/verl/models/llama/megatron/layers/parallel_linear.py
src/verl/models/llama/megatron/layers/parallel_mlp.py
src/verl/models/llama/megatron/layers/parallel_rmsnorm.py
src/verl/models/transformers/__init__.py
src/verl/models/transformers/llama.py
src/verl/models/transformers/monkey_patch.py
src/verl/models/transformers/qwen2.py
src/verl/single_controller/__init__.py
src/verl/single_controller/base/__init__.py
src/verl/single_controller/base/decorator.py
src/verl/single_controller/base/worker.py
src/verl/single_controller/base/worker_group.py
src/verl/single_controller/base/megatron/__init__.py
src/verl/single_controller/base/megatron/worker.py
src/verl/single_controller/base/megatron/worker_group.py
src/verl/single_controller/base/register_center/__init__.py
src/verl/single_controller/base/register_center/ray.py
src/verl/single_controller/ray/__init__.py
src/verl/single_controller/ray/base.py
src/verl/single_controller/ray/megatron.py
src/verl/third_party/__init__.py
src/verl/third_party/vllm/__init__.py
src/verl/third_party/vllm/vllm_spmd/__init__.py
src/verl/third_party/vllm/vllm_spmd/dtensor_weight_loaders.py
src/verl/third_party/vllm/vllm_v_0_3_1/__init__.py
src/verl/third_party/vllm/vllm_v_0_3_1/arg_utils.py
src/verl/third_party/vllm/vllm_v_0_3_1/config.py
src/verl/third_party/vllm/vllm_v_0_3_1/llm.py
src/verl/third_party/vllm/vllm_v_0_3_1/llm_engine_sp.py
src/verl/third_party/vllm/vllm_v_0_3_1/model_loader.py
src/verl/third_party/vllm/vllm_v_0_3_1/model_runner.py
src/verl/third_party/vllm/vllm_v_0_3_1/parallel_state.py
src/verl/third_party/vllm/vllm_v_0_3_1/tokenizer.py
src/verl/third_party/vllm/vllm_v_0_3_1/weight_loaders.py
src/verl/third_party/vllm/vllm_v_0_3_1/worker.py
src/verl/third_party/vllm/vllm_v_0_4_2/__init__.py
src/verl/third_party/vllm/vllm_v_0_4_2/arg_utils.py
src/verl/third_party/vllm/vllm_v_0_4_2/config.py
src/verl/third_party/vllm/vllm_v_0_4_2/dtensor_weight_loaders.py
src/verl/third_party/vllm/vllm_v_0_4_2/hf_weight_loader.py
src/verl/third_party/vllm/vllm_v_0_4_2/llm.py
src/verl/third_party/vllm/vllm_v_0_4_2/llm_engine_sp.py
src/verl/third_party/vllm/vllm_v_0_4_2/megatron_weight_loaders.py
src/verl/third_party/vllm/vllm_v_0_4_2/model_loader.py
src/verl/third_party/vllm/vllm_v_0_4_2/model_runner.py
src/verl/third_party/vllm/vllm_v_0_4_2/parallel_state.py
src/verl/third_party/vllm/vllm_v_0_4_2/spmd_gpu_executor.py
src/verl/third_party/vllm/vllm_v_0_4_2/tokenizer.py
src/verl/third_party/vllm/vllm_v_0_4_2/worker.py
src/verl/third_party/vllm/vllm_v_0_5_4/__init__.py
src/verl/third_party/vllm/vllm_v_0_5_4/arg_utils.py
src/verl/third_party/vllm/vllm_v_0_5_4/config.py
src/verl/third_party/vllm/vllm_v_0_5_4/dtensor_weight_loaders.py
src/verl/third_party/vllm/vllm_v_0_5_4/hf_weight_loader.py
src/verl/third_party/vllm/vllm_v_0_5_4/llm.py
src/verl/third_party/vllm/vllm_v_0_5_4/llm_engine_sp.py
src/verl/third_party/vllm/vllm_v_0_5_4/megatron_weight_loaders.py
src/verl/third_party/vllm/vllm_v_0_5_4/model_loader.py
src/verl/third_party/vllm/vllm_v_0_5_4/model_runner.py
src/verl/third_party/vllm/vllm_v_0_5_4/parallel_state.py
src/verl/third_party/vllm/vllm_v_0_5_4/spmd_gpu_executor.py
src/verl/third_party/vllm/vllm_v_0_5_4/tokenizer.py
src/verl/third_party/vllm/vllm_v_0_5_4/worker.py
src/verl/third_party/vllm/vllm_v_0_6_3/__init__.py
src/verl/third_party/vllm/vllm_v_0_6_3/arg_utils.py
src/verl/third_party/vllm/vllm_v_0_6_3/config.py
src/verl/third_party/vllm/vllm_v_0_6_3/dtensor_weight_loaders.py
src/verl/third_party/vllm/vllm_v_0_6_3/hf_weight_loader.py
src/verl/third_party/vllm/vllm_v_0_6_3/llm.py
src/verl/third_party/vllm/vllm_v_0_6_3/llm_engine_sp.py
src/verl/third_party/vllm/vllm_v_0_6_3/megatron_weight_loaders.py
src/verl/third_party/vllm/vllm_v_0_6_3/model_loader.py
src/verl/third_party/vllm/vllm_v_0_6_3/model_runner.py
src/verl/third_party/vllm/vllm_v_0_6_3/parallel_state.py
src/verl/third_party/vllm/vllm_v_0_6_3/spmd_gpu_executor.py
src/verl/third_party/vllm/vllm_v_0_6_3/tokenizer.py
src/verl/third_party/vllm/vllm_v_0_6_3/worker.py
src/verl/trainer/__init__.py
src/verl/trainer/fsdp_sft_trainer.py
src/verl/trainer/main_eval.py
src/verl/trainer/main_generation.py
src/verl/trainer/main_ppo.py
src/verl/trainer/runtime_env.yaml
src/verl/trainer/config/evaluation.yaml
src/verl/trainer/config/generation.yaml
src/verl/trainer/config/ppo_megatron_trainer.yaml
src/verl/trainer/config/ppo_trainer.yaml
src/verl/trainer/config/sft_trainer.yaml
src/verl/trainer/ppo/__init__.py
src/verl/trainer/ppo/core_algos.py
src/verl/trainer/ppo/ray_trainer.py
src/verl/utils/__init__.py
src/verl/utils/config.py
src/verl/utils/distributed.py
src/verl/utils/flops_counter.py
src/verl/utils/fs.py
src/verl/utils/fsdp_utils.py
src/verl/utils/hdfs_io.py
src/verl/utils/import_utils.py
src/verl/utils/logging_utils.py
src/verl/utils/megatron_utils.py
src/verl/utils/memory_buffer.py
src/verl/utils/model.py
src/verl/utils/py_functional.py
src/verl/utils/ray_utils.py
src/verl/utils/seqlen_balancing.py
src/verl/utils/tokenizer.py
src/verl/utils/torch_dtypes.py
src/verl/utils/torch_functional.py
src/verl/utils/tracking.py
src/verl/utils/ulysses.py
src/verl/utils/checkpoint/__init__.py
src/verl/utils/checkpoint/checkpoint_manager.py
src/verl/utils/checkpoint/fsdp_checkpoint_manager.py
src/verl/utils/dataset/__init__.py
src/verl/utils/dataset/rl_dataset.py
src/verl/utils/dataset/rm_dataset.py
src/verl/utils/dataset/sft_dataset.py
src/verl/utils/dataset/template.py
src/verl/utils/debug/__init__.py
src/verl/utils/debug/performance.py
src/verl/utils/debug/trajectory_tracker.py
src/verl/utils/logger/__init__.py
src/verl/utils/logger/aggregate_logger.py
src/verl/utils/megatron/__init__.py
src/verl/utils/megatron/memory.py
src/verl/utils/megatron/optimizer.py
src/verl/utils/megatron/optimizer_config.py
src/verl/utils/megatron/pipeline_parallel.py
src/verl/utils/megatron/sequence_parallel.py
src/verl/utils/megatron/tensor_parallel.py
src/verl/utils/rendezvous/__init__.py
src/verl/utils/rendezvous/ray_backend.py
src/verl/utils/reward_score/__init__.py
src/verl/utils/reward_score/gsm8k.py
src/verl/utils/reward_score/math.py
src/verl/utils/reward_score/re_search.py
src/verl/utils/reward_score/re_search_origin.py
src/verl/utils/reward_score/prime_code/__init__.py
src/verl/utils/reward_score/prime_code/testing_util.py
src/verl/utils/reward_score/prime_code/utils.py
src/verl/utils/reward_score/prime_math/__init__.py
src/verl/utils/reward_score/prime_math/grader.py
src/verl/utils/reward_score/prime_math/math_normalize.py
src/verl/workers/__init__.py
src/verl/workers/fsdp_workers.py
src/verl/workers/megatron_workers.py
src/verl/workers/actor/__init__.py
src/verl/workers/actor/base.py
src/verl/workers/actor/dp_actor.py
src/verl/workers/actor/megatron_actor.py
src/verl/workers/critic/__init__.py
src/verl/workers/critic/base.py
src/verl/workers/critic/dp_critic.py
src/verl/workers/critic/megatron_critic.py
src/verl/workers/reward_manager/__init__.py
src/verl/workers/reward_manager/naive.py
src/verl/workers/reward_manager/prime.py
src/verl/workers/reward_manager/re_search.py
src/verl/workers/reward_model/__init__.py
src/verl/workers/reward_model/base.py
src/verl/workers/reward_model/megatron/__init__.py
src/verl/workers/reward_model/megatron/reward_model.py
src/verl/workers/rollout/__init__.py
src/verl/workers/rollout/base.py
src/verl/workers/rollout/hf_rollout.py
src/verl/workers/rollout/tokenizer.py
src/verl/workers/rollout/naive/__init__.py
src/verl/workers/rollout/naive/naive_rollout.py
src/verl/workers/rollout/vllm_rollout/__init__.py
src/verl/workers/rollout/vllm_rollout/vllm_rollout.py
src/verl/workers/rollout/vllm_rollout/vllm_rollout_spmd.py
src/verl/workers/sharding_manager/__init__.py
src/verl/workers/sharding_manager/base.py
src/verl/workers/sharding_manager/fsdp_ulysses.py
src/verl/workers/sharding_manager/fsdp_vllm.py
src/verl/workers/sharding_manager/megatron_vllm.py