# Copyright 2024 Bytedance Ltd. and/or its affiliates
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""
The vllm_rollout that can be applied in different backend
When working with FSDP:
- Use DTensor weight loader (recommended) or HF weight loader
- Utilize state_dict from the FSDP to synchronize the weights among tp ranks in vLLM
When working with Megatron:
- Use Megatron weight loader
- During training, only the current pp stage holds the parameters
- Before inference, broadcast the parameters of the current pp rank to all other pp ranks (all pp ranks holds all the parameters)
- Bind the parameters to the inference engine
- Do inference in tp. pp is treated as additional dp
- After inference, all the parameters that doesn't belong to this pp rank is freed.
"""
from typing import List
from contextlib import contextmanager
from omegaconf import DictConfig
import torch
import torch.distributed
from tensordict import TensorDict
from torch import nn
import time
import requests
from functools import wraps
from typing import Union

from verl import DataProto
from verl.utils.torch_functional import get_eos_mask, pad_sequence_to_length
from verl.workers.rollout.base import BaseRollout
from verl.third_party.vllm import LLM, vllm_version
from verl.third_party.vllm import parallel_state as vllm_ps
from vllm import SamplingParams
# from ray.util.debugpy import set_trace

# TODO
# 1. support pp in vllm
# 2. passing tokenizer is not necessary? no encoding/decoding is happending here
# 3. simplify init logics


# NOTE(sgm): add for verl. We can optimize it by making the dataloader yield List[int] without padding.
def _pre_process_inputs(pad_token_id, prompt_token_ids: torch.Tensor) -> List[int]:
    # 返回一个整数列表(List[int])，表示去除左侧padding后的token ID序列
    # remove the left padding in the prompt token_id
    # pad_token_id = self.llm_engine.tokenizer.pad_token_id if self.llm_engine.tokenizer.pad_token_id is not None else self.llm_engine.tokenizer.eos_token_id
    non_pad_index = torch.nonzero(prompt_token_ids != pad_token_id, as_tuple=False)[0][0]
    token_ids = prompt_token_ids[non_pad_index:].tolist()
    return token_ids


class vLLMRollout(BaseRollout):

    def __init__(self, actor_module: nn.Module, config: DictConfig, tokenizer, model_hf_config, **kwargs):
        """A vLLM rollout. It requires the module is supported by the vllm.

        Args:
            module: module here follows huggingface APIs
            config: DictConfig
            tokenizer: the task/model tokenizer
            model_hf_config: the huggingface config to initiallize the generating model in vllm
            **kwargs: train_tp, for Megatron Backend to initialize hybrid engine (zero redundancy) process group
        """
        super().__init__()
        self.config = config
        assert not (not config.enforce_eager and config.free_cache_engine), \
            "disable CUDA graph (enforce_eager = False) if free cache engine"

        tensor_parallel_size = self.config.get('tensor_model_parallel_size', 1)
        assert tensor_parallel_size <= torch.distributed.get_world_size(), \
            "tensor parallel size should be less than or equal to the world size"
        max_num_batched_tokens = self.config.get('max_num_batched_tokens', 8192)

        if kwargs.get('train_tp', None) is not None:
            # deployed with megatron
            import os
            os.environ['CUDA_TIMER_STREAM_KAFKA_ENABLE'] = '0'
            os.environ['MEGATRON_IMPORT_TIMERS'] = '0'
            train_tp = kwargs.get('train_tp', None)
            num_tp_per_train_tp = train_tp // tensor_parallel_size
            if vllm_version in ('0.4.2', '0.5.4', '0.6.3'):
                vllm_ps.initialize_parallel_state(tensor_model_parallel_size=tensor_parallel_size,
                                                  num_tp_per_train_tp=num_tp_per_train_tp)

        assert model_hf_config.max_position_embeddings >= config.prompt_length + config.response_length, \
            "model context length should be greater than total sequence length"
        self.inference_engine = LLM(
            actor_module,
            tokenizer=tokenizer,
            model_hf_config=model_hf_config,
            tensor_parallel_size=tensor_parallel_size,
            dtype=config.dtype,
            enforce_eager=config.enforce_eager,
            gpu_memory_utilization=config.gpu_memory_utilization,
            skip_tokenizer_init=False,
            max_model_len=config.prompt_length + config.response_length,
            load_format=config.load_format,
            disable_log_stats=config.disable_log_stats,
            max_num_batched_tokens=max_num_batched_tokens,
            enable_chunked_prefill=config.enable_chunked_prefill,
        )

        # Offload vllm model to reduce peak memory usage
        self.inference_engine.offload_model_weights()

        kwargs = dict(
            n=1,
            logprobs=1,  # can be set to 0 and let actor to recompute
            max_tokens=config.response_length,
        )

        # we may detokenize the result all together later
        if vllm_version in ('0.4.2', '0.5.4', '0.6.3'):
            kwargs['detokenize'] = False

        # supporting adding any sampling params from the config file
        for k in config.keys():
            if hasattr(SamplingParams(), str(k)):
                kwargs[k] = config.get(k)

        print(f"kwargs: {kwargs}")
        self.sampling_params = SamplingParams(**kwargs)

        self.pad_token_id = tokenizer.pad_token_id

    @contextmanager
    def update_sampling_params(self, **kwargs):
        # update sampling params
        old_sampling_params_args = {}
        if kwargs:
            for key, value in kwargs.items():
                if hasattr(self.sampling_params, key):
                    old_value = getattr(self.sampling_params, key)
                    old_sampling_params_args[key] = old_value
                    setattr(self.sampling_params, key, value)
        yield
        # roll back to previous sampling params
        # if len(old_sampling_params_args):
        for key, value in old_sampling_params_args.items():
            setattr(self.sampling_params, key, value)

    @torch.no_grad()
    def generate_sequences(self, prompts: DataProto, **kwargs) -> DataProto:
        # rebuild vllm cache engine
        if self.config.free_cache_engine:
            self.inference_engine.init_cache_engine()

        idx = prompts.batch['input_ids']  # (bs, prompt_length)
        # left-padded attention_mask
        attention_mask = prompts.batch['attention_mask']
        position_ids = prompts.batch['position_ids']

        # used to construct attention_mask
        eos_token_id = prompts.meta_info['eos_token_id']

        batch_size = idx.size(0)

        idx_list = []
        # parse idx from torch.Tensor to List[List[str]]
        for i in range(batch_size):
            idx_list.append(_pre_process_inputs(self.pad_token_id, idx[i]))

        do_sample = prompts.meta_info.get('do_sample', True)
        if not do_sample:
            kwargs = {
                'best_of': 1,
                'top_p': 1.0,
                'top_k': -1,
                'min_p': 0.0,
                'temperature': 0,
                'n': 1  # if greedy, only 1 response
            }

        # users can customize different sampling_params at different run
        with self.update_sampling_params(**kwargs):
            output = self.inference_engine.generate(
                prompts=None,  # because we have already convert it to prompt token id
                sampling_params=self.sampling_params,
                prompt_token_ids=idx_list,
                use_tqdm=False)

        # TODO(sgm): disable logprob when recompute_log_prob is enable
        # if n = 1: (bs, response_length) ; if n > 1: (bs * n, response_length)
        response = output[0].to(idx.device)
        log_probs = output[1].to(idx.device)

        if response.shape[1] < self.config.response_length:
            response = pad_sequence_to_length(response, self.config.response_length, self.pad_token_id)
            log_probs = pad_sequence_to_length(log_probs, self.config.response_length, self.pad_token_id)

        if self.config.n > 1 and do_sample:
            idx = idx.repeat_interleave(self.config.n, dim=0)
            attention_mask = attention_mask.repeat_interleave(self.config.n, dim=0)
            position_ids = position_ids.repeat_interleave(self.config.n, dim=0)
            batch_size = batch_size * self.config.n
        seq = torch.cat([idx, response], dim=-1)

        response_length = response.size(1)
        delta_position_id = torch.arange(1, response_length + 1, device=position_ids.device)
        delta_position_id = delta_position_id.unsqueeze(0).repeat(batch_size, 1)

        # TODO(sgm): fix position_ids on right_pad
        # prompt: left pad + response: right pad
        # attention_mask: [0,0,0,0,1,1,1,1, | 1,1,1,0,0,0,0,0]
        # position_ids:   [0,0,0,0,0,1,2,3, | 4,5,6,7,8,9,10,11]
        response_position_ids = position_ids[:, -1:] + delta_position_id
        position_ids = torch.cat([position_ids, response_position_ids], dim=-1)
        response_attention_mask = get_eos_mask(response_id=response, eos_token=eos_token_id, dtype=attention_mask.dtype)
        attention_mask = torch.cat((attention_mask, response_attention_mask), dim=-1)

        # all the tp ranks should contain the same data here. data in all ranks are valid
        batch = TensorDict(
            {
                'prompts': idx,
                'responses': response,
                'input_ids': seq,  # here input_ids become the whole sentences
                # 'old_log_probs': log_probs, # we will recompute old log prob with actor
                'attention_mask': attention_mask,
                'position_ids': position_ids
            },
            batch_size=batch_size)

        # free vllm cache engine
        if self.config.free_cache_engine:
            self.inference_engine.free_cache_engine()

        return DataProto(batch=batch)

# def retry(max: int=10, sleep: int=1):
#     def decorator(func):
#         @wraps(func)
#         def wrapper(*args, **kwargs):
#             for i in range(max):
#                 try:
#                     return func(*args, **kwargs)
#                 except Exception as e:
#                     if i == max - 1:
#                         print(f"Retry {func.__name__} failed after {max} times")
#                     elif sleep:
#                         time.sleep(sleep)
#         return wrapper
#     return decorator
def retry(max: int=10, sleep: int=1):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for i in range(max):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if i == max - 1:
                        print(f"Retry {func.__name__} failed after {max} times, error: {str(e)}")
                    elif sleep:
                        time.sleep(sleep)
        return wrapper
    return decorator

class vLLMRolloutWithSearch(vLLMRollout):
    def __init__(self, actor_module: nn.Module, config: DictConfig, tokenizer, model_hf_config, **kwargs):
        super().__init__(actor_module, config, tokenizer, model_hf_config, **kwargs)
        self.tokenizer = tokenizer

    # @retry(max=5, sleep=1)
    # def batch_search(self, query: Union[str, List[str]], top_n=5) -> List[str]:
    #     # 根据query搜索top_n个结果
    #     # 返回结果为list[str]
    #     # 如果query为空，则返回'invalid query'
    #     if len(query) == 0:
    #         return 'invalid query'

    #     url = f'{self.config.search_url}/batch_search'
    #     if isinstance(query, str):
    #         query = [query]
    #     data = {'query': query, 'top_n': top_n}
    #     response = requests.post(url, json=data)
        
    #     result_list = []
    #     for item in response.json():
    #         curr_result = ''
    #         for line in item:
    #             curr_result += f"{line['contents']}\n\n"
    #         result_list.append(curr_result.strip())
        
    #     return result_list
    @retry(max=5, sleep=1)
    def batch_search(self, query: Union[str, List[str]], top_n=5) -> List[str]:
        '''
        批量搜索: 根据 n 个 query 搜索 n * top_n 个结果
        Args:
            query: 一个字符串或一个包含多个字符串的列表
            top_n: 每个query搜索的top_n个结果
        Returns:
            result_list: 一个包含n个字符串的列表，每个字符串表示一个query的搜索结果
        '''
        # 如果query为空，则返回'invalid query'
        if len(query) == 0:
            return 'invalid query'

        # 1. 如果 query 为列表，分别对每个query进行搜索
        if isinstance(query, list):
            # 创建一个结果列表，长度与输入query列表相同
            result_list = []
            valid_queries = []
            valid_indices = []
            
            # 遍历所有查询，记录有效查询及其索引
            for i, q in enumerate(query):
                if q == '':
                    result_list.append('invalid query')
                else:
                    valid_queries.append(q)
                    valid_indices.append(i)
            
            # 如果有有效查询，进行批量搜索
            if valid_queries:
                url = f'{self.config.search_url}/batch_search'
                data = {'query': valid_queries, 'top_n': top_n}
                response = requests.post(url, json=data)
                
                # 处理搜索结果
                for idx, item in zip(valid_indices, response.json()):
                    curr_result = ''
                    for line in item:
                        curr_result += f"{line['contents']}\n\n"
                    result_list.insert(idx, curr_result.strip())
            
            return result_list
        
        # 2. 如果 query 为单个字符串，则进行单个搜索
        
        url = f'{self.config.search_url}/batch_search'
        data = {'query': [query], 'top_n': top_n}
        response = requests.post(url, json=data)
        
        result_list = [
            "\n\n".join(line['contents'] for line in item).strip()
            for item in response.json()
        ]
        return result_list

    @retry(max=5, sleep=1)
    def search(self, query: str):
        if query == '':
            return 'invalid query'

        url = f'{self.config.search_url}/search'
        data = {'query': query, 'top_n': 5}
        response = requests.post(url, json=data)
        retrieval_text = ''
        for line in response.json():
            retrieval_text += f"{line['contents']}\n\n"
        retrieval_text = retrieval_text.strip()
        return retrieval_text

    def extract_search_content(self, text: str) -> str:
        try:
            start_tag = '<query>'
            end_tag = '</query>'
            end_pos = text.rindex(end_tag)
            start_pos = text.rindex(start_tag, 0, end_pos)
            return text[start_pos + len(start_tag):end_pos].strip()
        except ValueError:
            return ""

    @torch.no_grad()
    def generate_sequences(self, prompts: DataProto, **kwargs) -> DataProto:
        # 获取max_time参数，默认为无限制(-1)
        max_time = kwargs.pop('max_time', -1) if 'max_time' in kwargs else prompts.meta_info.get('max_time', -1)
        max_time = 5
        # set_trace()
        # print(f"max_time: {max_time}")s
        # assert max_time == -1, "max_time is not supported in vLLM Rollout with search"

        # rebuild vllm cache engine
        if self.config.free_cache_engine:
            self.inference_engine.init_cache_engine()

        ori_input_ids = prompts.batch['input_ids']  # (bs, prompt_length)

        # left-padded attention_mask
        attention_mask = prompts.batch['attention_mask']
        position_ids = prompts.batch['position_ids']

        # used to construct attention_mask
        eos_token_id = prompts.meta_info['eos_token_id']

        batch_size = ori_input_ids.size(0)

        idx_list = []
        # parse idx from torch.Tensor to List[List[str]]
        for i in range(batch_size):
            idx_list.append(_pre_process_inputs(self.pad_token_id, ori_input_ids[i]))

        do_sample = prompts.meta_info.get('do_sample', True)
        if not do_sample:
            kwargs = {
                'best_of': 1,
                'top_p': 1.0,
                'top_k': -1,
                'min_p': 0.0,
                'temperature': 0,
                'n': 1  # if greedy, only 1 response
            }

        with self.update_sampling_params(**kwargs):
            # prepare n copies for each input
            curr_inputs = []
            for input_ids in idx_list:
                for _ in range(self.sampling_params.n):
                    curr_inputs.append(input_ids.copy())
            init_inputs = [ids.copy() for ids in curr_inputs]
            
            curr_max_tokens = [self.sampling_params.max_tokens] * len(curr_inputs)
            active_indices = list(range(len(curr_inputs)))
            retrieval_counts = [0] * len(curr_inputs)
            search_documents = [[] for _ in range(len(curr_inputs))]
            result_mask_list = [[] for _ in range(len(curr_inputs))]

            # Track the cumulative number of tokens fed into the inference engine for each sequence
            tokens_fed_to_inference_counts = [0] * len(curr_inputs)

            # generate until all inputs are finished
            while active_indices:
                # only process the active inputs
                active_inputs_for_generation = [curr_inputs[i] for i in active_indices]
                active_max_tokens = [curr_max_tokens[i] for i in active_indices]

                # For each active sequence, add the length of its current input (which will be fed to the model)
                # to its cumulative count of tokens fed to inference.
                for active_idx_in_loop in active_indices:
                    tokens_fed_to_inference_counts[active_idx_in_loop] += len(curr_inputs[active_idx_in_loop])

                with self.update_sampling_params(n=1, stop=['</query>', '</documents_refine>'], max_tokens=max(active_max_tokens), detokenize=True):
                    outputs = self.inference_engine.generate(
                        prompts=None,
                        sampling_params=self.sampling_params,
                        prompt_token_ids=active_inputs_for_generation, # Use the prepared active_inputs
                        use_tqdm=False
                    )
                
                search_queries = []
                search_indices = []
                new_active_indices = []

                for i, idx in enumerate(active_indices):
                    output_ids_raw = outputs[0][i] # This is still the raw output from model for THIS step
                    output_ids = output_ids_raw.tolist()
                    
                    if self.tokenizer.eos_token_id in output_ids:
                        first_eos_idx = output_ids.index(self.tokenizer.eos_token_id)
                    else:
                        first_eos_idx = len(output_ids)
                    
                    if self.tokenizer.pad_token_id in output_ids:
                        first_pad_idx = output_ids.index(self.tokenizer.pad_token_id)
                    else:
                        first_pad_idx = len(output_ids)
                    
                    finish_reason = outputs[2][i]
                    stop_reason = outputs[3][i]

                    processed_output_ids = [] # To store the tokens actually appended
                    if finish_reason == 'stop' and isinstance(stop_reason, str):
                        if '</query>' in stop_reason:
                            if max_time > 0 and retrieval_counts[idx] >= max_time:
                                processed_output_ids = output_ids[:first_pad_idx]
                                curr_inputs[idx] += processed_output_ids
                                result_mask_list[idx] += [1] * len(processed_output_ids)
                                assert len(curr_inputs[idx]) - len(init_inputs[idx]) == len(result_mask_list[idx])
                            else:
                                processed_output_ids = output_ids[:first_pad_idx]
                                output_str = self.tokenizer.decode(processed_output_ids)
                                search_content = self.extract_search_content(output_str)
                                search_queries.append(search_content)
                                search_indices.append(idx)
                                new_active_indices.append(idx)
                                curr_inputs[idx] += processed_output_ids
                                result_mask_list[idx] += [1] * len(processed_output_ids)
                                retrieval_counts[idx] += 1
                                assert len(curr_inputs[idx]) - len(init_inputs[idx]) == len(result_mask_list[idx])
                        elif '</documents_refine>' in stop_reason:
                            processed_output_ids = output_ids[:first_pad_idx]
                            if 0 in result_mask_list[idx]:
                                full_tokens = curr_inputs[idx][len(init_inputs[idx]):]
                                full_mask = result_mask_list[idx]
                                relevant_info_tokens = self.tokenizer.encode("<documents_refine>")
                                relevant_info_length = len(relevant_info_tokens)
                                last_zero_start = -1
                                last_zero_end = -1
                                for j_loop_var in range(len(full_mask)-1, -1, -1):
                                    if full_mask[j_loop_var] == 0:
                                        last_zero_end = j_loop_var + 1
                                        break
                                if last_zero_end != -1:
                                    last_zero_start = last_zero_end
                                    for j_loop_var in range(last_zero_end-1, -1, -1):
                                        if full_mask[j_loop_var] != 0:
                                            last_zero_start = j_loop_var + 1
                                            break
                                        if j_loop_var == 0:
                                            last_zero_start = 0
                                if (last_zero_start != -1 and last_zero_end != -1 \
                                    and last_zero_end - last_zero_start > relevant_info_length):
                                    last_zero_end = max(last_zero_end - relevant_info_length, 0)
                                    curr_inputs[idx] = init_inputs[idx] + full_tokens[:last_zero_start] + full_tokens[last_zero_end:]
                                    result_mask_list[idx] = full_mask[:last_zero_start] + full_mask[last_zero_end:]
                            curr_inputs[idx] += processed_output_ids
                            result_mask_list[idx] += [1] * len(processed_output_ids)
                            generated_length = len(curr_inputs[idx]) - len(init_inputs[idx])
                            assert len(result_mask_list[idx]) == generated_length
                            new_active_indices.append(idx)
                    elif finish_reason == 'stop' and stop_reason == None:
                        processed_output_ids = output_ids[:first_eos_idx+1]
                        curr_inputs[idx] += processed_output_ids
                        result_mask_list[idx] += [1] * len(processed_output_ids)
                        assert len(curr_inputs[idx]) - len(init_inputs[idx]) == len(result_mask_list[idx])
                    elif finish_reason == 'stop' and stop_reason == self.tokenizer.pad_token_id:
                        processed_output_ids = output_ids[:first_pad_idx+1]
                        curr_inputs[idx] += processed_output_ids
                        result_mask_list[idx] += [1] * len(processed_output_ids)
                        assert len(curr_inputs[idx]) - len(init_inputs[idx]) == len(result_mask_list[idx])
                    elif finish_reason == 'length':
                        processed_output_ids = output_ids
                        curr_inputs[idx] += processed_output_ids
                        result_mask_list[idx] += [1] * len(processed_output_ids)
                        assert len(curr_inputs[idx]) - len(init_inputs[idx]) == len(result_mask_list[idx])
                        
                if search_queries:
                    search_results = self.batch_search(search_queries)
                    for search_idx_loop, result in zip(search_indices, search_results):
                        search_documents[search_idx_loop].append(result)
                        if result == "":
                            print(f"search : retrieval_counts: {retrieval_counts[search_idx_loop]}, curr_inputs[search_idx_loop]: {self.tokenizer.decode(curr_inputs[search_idx_loop])}")
                        search_result_ids = self.tokenizer.encode(f" <documents>\n{result}\n</documents> <documents_refine>")
                        curr_inputs[search_idx_loop] += search_result_ids
                        result_mask_list[search_idx_loop] += [0] * len(search_result_ids)
                        assert len(curr_inputs[search_idx_loop]) - len(init_inputs[search_idx_loop]) == len(result_mask_list[search_idx_loop])

                length_checked_active_indices = []
                for active_idx_check in active_indices:
                    assert len(curr_inputs[active_idx_check]) - len(init_inputs[active_idx_check]) == len(result_mask_list[active_idx_check])
                    if len(curr_inputs[active_idx_check]) - len(init_inputs[active_idx_check]) >= self.config.response_length:
                        curr_inputs[active_idx_check] = init_inputs[active_idx_check] \
                            + curr_inputs[active_idx_check][len(init_inputs[active_idx_check]):len(init_inputs[active_idx_check])+self.config.response_length]
                        result_mask_list[active_idx_check] = result_mask_list[active_idx_check][:self.config.response_length]
                    else:
                        curr_max_tokens[active_idx_check] = self.config.response_length - len(curr_inputs[active_idx_check]) + len(init_inputs[active_idx_check])
                        if active_idx_check in new_active_indices:
                            length_checked_active_indices.append(active_idx_check)
                    assert retrieval_counts[active_idx_check] == len(search_documents[active_idx_check])
                active_indices = length_checked_active_indices

            output_ids_list = []
            final_retrieval_counts = []
            final_search_documents = []
            final_tokens_fed_to_inference = [] # New list
            for i_loop_final, input_ids_item in enumerate(idx_list):
                for j_loop_final in range(self.sampling_params.n):
                    current_idx_final = i_loop_final * self.sampling_params.n + j_loop_final
                    input_len = len(input_ids_item)
                    output_ids_list.append(curr_inputs[current_idx_final][input_len:])
                    final_retrieval_counts.append(retrieval_counts[current_idx_final])
                    final_search_documents.append(search_documents[current_idx_final])
                    final_tokens_fed_to_inference.append(tokens_fed_to_inference_counts[current_idx_final]) # Collect

        response_list = []
        result_mask_list_padded = []
        for output_ids_item, result_mask_item in zip(output_ids_list, result_mask_list):
            assert len(output_ids_item) == len(result_mask_item)
            response_tensor = torch.tensor(output_ids_item, device=ori_input_ids.device)
            response_tensor = pad_sequence_to_length(response_tensor, self.config.response_length, self.pad_token_id)
            result_mask_tensor = torch.tensor(result_mask_item, device=ori_input_ids.device)
            result_mask_tensor = pad_sequence_to_length(result_mask_tensor, self.config.response_length, 0)
            response_list.append(response_tensor)
            result_mask_list_padded.append(result_mask_tensor)

        response = torch.stack(response_list, dim=0)
        result_mask = torch.stack(result_mask_list_padded, dim=0)

        if self.config.n > 1 and do_sample:
            ori_input_ids = ori_input_ids.repeat_interleave(self.config.n, dim=0)
            attention_mask = attention_mask.repeat_interleave(self.config.n, dim=0)
            position_ids = position_ids.repeat_interleave(self.config.n, dim=0)
            batch_size = batch_size * self.config.n
            
        seq = torch.cat([ori_input_ids, response], dim=-1)
        response_length = response.size(1)
        delta_position_id = torch.arange(1, response_length + 1, device=position_ids.device)
        delta_position_id = delta_position_id.unsqueeze(0).repeat(batch_size, 1)
        response_position_ids = position_ids[:, -1:] + delta_position_id
        position_ids = torch.cat([position_ids, response_position_ids], dim=-1)
        response_attention_mask = get_eos_mask(response_id=response, eos_token=eos_token_id, dtype=attention_mask.dtype)
        attention_mask = torch.cat((attention_mask, response_attention_mask), dim=-1)
        loss_mask = result_mask * response_attention_mask

        for i_loop_var in range(len(final_retrieval_counts)):
            if final_retrieval_counts[i_loop_var] > 0:
                final_search_documents[i_loop_var] = [final_search_documents[i_loop_var][-1]]

        retrieval_count_tensor = torch.tensor(final_retrieval_counts, device=ori_input_ids.device)
        # Convert the collected counts to a tensor
        tokens_fed_to_inference_tensor = torch.tensor(final_tokens_fed_to_inference, device=ori_input_ids.device)

        MAX_DOC_LENGTH = self.config.response_length
        encoded_docs = []
        for docs in final_search_documents:
            if not docs:
                doc_tensor = torch.full((1,), self.pad_token_id, dtype=torch.long, device=ori_input_ids.device)
            else:
                combined_doc = docs[0]
                encoded = self.tokenizer.encode(combined_doc)[:MAX_DOC_LENGTH]
                doc_tensor = torch.tensor(encoded, dtype=torch.long, device=ori_input_ids.device)
            encoded_docs.append(doc_tensor)

        search_docs_tensor_list = [pad_sequence_to_length(doc_tensor, MAX_DOC_LENGTH, self.pad_token_id) for doc_tensor in encoded_docs]
        search_docs_tensor = torch.stack(search_docs_tensor_list, dim=0)

        batch = TensorDict({
            'prompts': ori_input_ids,
            'responses': response,
            'input_ids': seq,
            'attention_mask': attention_mask,
            'loss_mask': loss_mask,
            'position_ids': position_ids,
            'retrieval_counts': retrieval_count_tensor,
            'search_documents': search_docs_tensor,
            'tokens_fed_to_inference': tokens_fed_to_inference_tensor # Add the new tensor
        }, batch_size=batch_size)

        if self.config.free_cache_engine:
            self.inference_engine.free_cache_engine()

        return DataProto(batch=batch)
