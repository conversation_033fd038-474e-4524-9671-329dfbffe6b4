import requests
import json

url = "https://google.serper.dev/search"

payload = json.dumps({
    "q": "apple inc",
    "location": "United States"
})
headers = {
    'X-API-KEY': '3fa11d6a07bf67b82798ebefb2787ad9e84f21fd',
    'Content-Type': 'application/json'
}

response = requests.request("POST", url, headers=headers, data=payload)

# 将 response 按照 json 格式进行存储
response_json = json.loads(response.text)
# 保存为文件
with open("serper.json", "w") as f:
    json.dump(response_json, f, indent=4)

print(response_json)
