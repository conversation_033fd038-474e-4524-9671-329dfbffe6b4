#!/bin/bash
# nvidia-smi

# Change directory to the training scripts folder
# cd /home/<USER>/code/personal/S9058924/SRR1/scripts/train || exit
cd /home/<USER>/code/personal/S9058924/SRR1 || exit

# Add the source directory to Python path
export PYTHONPATH="/home/<USER>/code/personal/S9058924/SRR1/src:$PYTHONPATH"
export WANDB_API_KEY=****************************************
export http_proxy=http://nbproxy.mlp.oppo.local:8888
export https_proxy=http://nbproxy.mlp.oppo.local:8888

# Ensure project verl has higher priority than conda verl
# export PYTHONPATH="/home/<USER>/code/personal/S9058924/SRR1/src/verl:$PYTHONPATH"
# # 验证登录
# wandb login --relogin
# 获取当前日期时间并格式化为年月日时分秒
# --actor_model_path /home/<USER>/data/group/model_hub/huggingface/Qwen/Qwen2.5-3B-Instruct \

current_datetime=$(date +"%y%m%d%H%M%S")

bash /home/<USER>/code/personal/S9058924/SRR1/scripts/eval/eval.sh \
    --train_batch_size 4 \
    --ppo_mini_batch_size 4 \
    --apply_chat True \
    --rollout_name vllm_with_search_accelerate \
    --prompt_template_name re_search_template_sys \
    --actor_model_path /home/<USER>/data/personal/S9058924/SRR1/output/7B-high2-both-kl-250618113341/global_step_200/actor/huggingface \
    --search_url "http://*************:8000" \
    --project_name research \
    --experiment_name "eval-7B-$current_datetime" \
    --nnodes 1 \
    --n_gpus_per_node 2 \
    --save_freq 100 \
    --test_freq 5 \
    --total_epochs 4 \
    --max_retrieval_time 5 \
    --wandb_api_key **************************************** \
    --save_path /home/<USER>/data/personal/S9058924/SRR1/output/eval-7B-$current_datetime \
    --test_files /home/<USER>/code/personal/S9058924/SRR1/data/bamboogle/val10.parquet \
