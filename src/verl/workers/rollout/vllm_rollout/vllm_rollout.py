# Copyright 2024 Bytedance Ltd. and/or its affiliates
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
The vllm_rollout that can be applied in different backend
When working with FSDP:
- Use DTensor weight loader (recommended) or HF weight loader
- Utilize state_dict from the FSDP to synchronize the weights among tp ranks in vLLM
When working with Megatron:
- Use Megatron weight loader
- During training, only the current pp stage holds the parameters
- Before inference, broadcast the parameters of the current pp rank to all other pp ranks (all pp ranks holds all the parameters)
- Bind the parameters to the inference engine
- Do inference in tp. pp is treated as additional dp
- After inference, all the parameters that doesn't belong to this pp rank is freed.
"""
from typing import List
from contextlib import contextmanager
from omegaconf import DictConfig
import torch
import torch.distributed
from tensordict import TensorDict
from torch import nn
import time
import requests
from functools import wraps
from typing import Union

from verl import DataProto
from verl.utils.torch_functional import get_eos_mask, pad_sequence_to_length
from verl.workers.rollout.base import BaseRollout
from verl.third_party.vllm import LLM, vllm_version
from verl.third_party.vllm import parallel_state as vllm_ps
from vllm import SamplingParams
import json
# from ray.util.debug import ray_breakpoint

# TODO
# 1. support pp in vllm
# 2. passing tokenizer is not necessary? no encoding/decoding is happending here
# 3. simplify init logics


# NOTE(sgm): add for verl. We can optimize it by making the dataloader yield List[int] without padding.
def _pre_process_inputs(pad_token_id, prompt_token_ids: torch.Tensor) -> List[int]:
    # 返回一个整数列表(List[int])，表示去除左侧padding后的token ID序列
    # remove the left padding in the prompt token_id
    # pad_token_id = self.llm_engine.tokenizer.pad_token_id if self.llm_engine.tokenizer.pad_token_id is not None else self.llm_engine.tokenizer.eos_token_id
    non_pad_index = torch.nonzero(prompt_token_ids != pad_token_id, as_tuple=False)[0][0]
    token_ids = prompt_token_ids[non_pad_index:].tolist()
    return token_ids


class vLLMRollout(BaseRollout):

    def __init__(self, actor_module: nn.Module, config: DictConfig, tokenizer, model_hf_config, **kwargs):
        """A vLLM rollout. It requires the module is supported by the vllm.

        Args:
            module: module here follows huggingface APIs
            config: DictConfig
            tokenizer: the task/model tokenizer
            model_hf_config: the huggingface config to initiallize the generating model in vllm
            **kwargs: train_tp, for Megatron Backend to initialize hybrid engine (zero redundancy) process group
        """
        super().__init__()
        self.config = config
        assert not (not config.enforce_eager and config.free_cache_engine), \
            "disable CUDA graph (enforce_eager = False) if free cache engine"

        tensor_parallel_size = self.config.get('tensor_model_parallel_size', 1)
        assert tensor_parallel_size <= torch.distributed.get_world_size(), \
            "tensor parallel size should be less than or equal to the world size"
        max_num_batched_tokens = self.config.get('max_num_batched_tokens', 8192)

        if kwargs.get('train_tp', None) is not None:
            # deployed with megatron
            import os
            os.environ['CUDA_TIMER_STREAM_KAFKA_ENABLE'] = '0'
            os.environ['MEGATRON_IMPORT_TIMERS'] = '0'
            train_tp = kwargs.get('train_tp', None)
            num_tp_per_train_tp = train_tp // tensor_parallel_size
            if vllm_version in ('0.4.2', '0.5.4', '0.6.3'):
                vllm_ps.initialize_parallel_state(tensor_model_parallel_size=tensor_parallel_size,
                                                  num_tp_per_train_tp=num_tp_per_train_tp)

        assert model_hf_config.max_position_embeddings >= config.prompt_length + config.response_length, \
            "model context length should be greater than total sequence length"
        self.inference_engine = LLM(
            actor_module,
            tokenizer=tokenizer,
            model_hf_config=model_hf_config,
            tensor_parallel_size=tensor_parallel_size,
            dtype=config.dtype,
            enforce_eager=config.enforce_eager,
            gpu_memory_utilization=config.gpu_memory_utilization,
            skip_tokenizer_init=False,
            max_model_len=config.prompt_length + config.response_length,
            load_format=config.load_format,
            disable_log_stats=config.disable_log_stats,
            max_num_batched_tokens=max_num_batched_tokens,
            enable_chunked_prefill=config.enable_chunked_prefill,
        )

        # Offload vllm model to reduce peak memory usage
        self.inference_engine.offload_model_weights()

        kwargs = dict(
            n=1,
            logprobs=1,  # can be set to 0 and let actor to recompute
            max_tokens=config.response_length,
        )

        # we may detokenize the result all together later
        if vllm_version in ('0.4.2', '0.5.4', '0.6.3'):
            kwargs['detokenize'] = False

        # supporting adding any sampling params from the config file
        for k in config.keys():
            if hasattr(SamplingParams(), str(k)):
                kwargs[k] = config.get(k)

        print(f"kwargs: {kwargs}")
        self.sampling_params = SamplingParams(**kwargs)

        self.pad_token_id = tokenizer.pad_token_id

    @contextmanager
    def update_sampling_params(self, **kwargs):
        # update sampling params
        old_sampling_params_args = {}
        if kwargs:
            for key, value in kwargs.items():
                if hasattr(self.sampling_params, key):
                    old_value = getattr(self.sampling_params, key)
                    old_sampling_params_args[key] = old_value
                    setattr(self.sampling_params, key, value)
        yield
        # roll back to previous sampling params
        # if len(old_sampling_params_args):
        for key, value in old_sampling_params_args.items():
            setattr(self.sampling_params, key, value)

    @torch.no_grad()
    def generate_sequences(self, prompts: DataProto, **kwargs) -> DataProto:
        # rebuild vllm cache engine
        if self.config.free_cache_engine:
            self.inference_engine.init_cache_engine()

        idx = prompts.batch['input_ids']  # (bs, prompt_length)
        # left-padded attention_mask
        attention_mask = prompts.batch['attention_mask']
        position_ids = prompts.batch['position_ids']

        # used to construct attention_mask
        eos_token_id = prompts.meta_info['eos_token_id']

        batch_size = idx.size(0)

        idx_list = []
        # parse idx from torch.Tensor to List[List[str]]
        for i in range(batch_size):
            idx_list.append(_pre_process_inputs(self.pad_token_id, idx[i]))

        do_sample = prompts.meta_info.get('do_sample', True)
        if not do_sample:
            kwargs = {
                'best_of': 1,
                'top_p': 1.0,
                'top_k': -1,
                'min_p': 0.0,
                'temperature': 0,
                'n': 1  # if greedy, only 1 response
            }

        # users can customize different sampling_params at different run
        with self.update_sampling_params(**kwargs):
            output = self.inference_engine.generate(
                prompts=None,  # because we have already convert it to prompt token id
                sampling_params=self.sampling_params,
                prompt_token_ids=idx_list,
                use_tqdm=False)

        # TODO(sgm): disable logprob when recompute_log_prob is enable
        # if n = 1: (bs, response_length) ; if n > 1: (bs * n, response_length)
        response = output[0].to(idx.device)
        log_probs = output[1].to(idx.device)

        if response.shape[1] < self.config.response_length:
            response = pad_sequence_to_length(response, self.config.response_length, self.pad_token_id)
            log_probs = pad_sequence_to_length(log_probs, self.config.response_length, self.pad_token_id)

        if self.config.n > 1 and do_sample:
            idx = idx.repeat_interleave(self.config.n, dim=0)
            attention_mask = attention_mask.repeat_interleave(self.config.n, dim=0)
            position_ids = position_ids.repeat_interleave(self.config.n, dim=0)
            batch_size = batch_size * self.config.n
        seq = torch.cat([idx, response], dim=-1)

        response_length = response.size(1)
        delta_position_id = torch.arange(1, response_length + 1, device=position_ids.device)
        delta_position_id = delta_position_id.unsqueeze(0).repeat(batch_size, 1)

        # TODO(sgm): fix position_ids on right_pad
        # prompt: left pad + response: right pad
        # attention_mask: [0,0,0,0,1,1,1,1, | 1,1,1,0,0,0,0,0]
        # position_ids:   [0,0,0,0,0,1,2,3, | 4,5,6,7,8,9,10,11]
        response_position_ids = position_ids[:, -1:] + delta_position_id
        position_ids = torch.cat([position_ids, response_position_ids], dim=-1)
        response_attention_mask = get_eos_mask(response_id=response, eos_token=eos_token_id, dtype=attention_mask.dtype)
        attention_mask = torch.cat((attention_mask, response_attention_mask), dim=-1)

        # all the tp ranks should contain the same data here. data in all ranks are valid
        batch = TensorDict(
            {
                'prompts': idx,
                'responses': response,
                'input_ids': seq,  # here input_ids become the whole sentences
                # 'old_log_probs': log_probs, # we will recompute old log prob with actor
                'attention_mask': attention_mask,
                'position_ids': position_ids
            },
            batch_size=batch_size)

        # free vllm cache engine
        if self.config.free_cache_engine:
            self.inference_engine.free_cache_engine()

        return DataProto(batch=batch)

def retry(max: int=10, sleep: int=1):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for i in range(max):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if i == max - 1:
                        print(f"Retry {func.__name__} failed after {max} times, error: {str(e)}")
                    elif sleep:
                        time.sleep(sleep)
        return wrapper
    return decorator

class vLLMRolloutWithSearch(vLLMRollout):
    def __init__(self, actor_module: nn.Module, config: DictConfig, tokenizer, model_hf_config, **kwargs):
        super().__init__(actor_module, config, tokenizer, model_hf_config, **kwargs)
        self.tokenizer = tokenizer

    @retry(max=5, sleep=1)
    def batch_search(self, query: Union[str, List[str]], top_n=5) -> List[str]:
        '''
        批量搜索: 根据 n 个 query 搜索 n * top_n 个结果
        Args:
            query: 一个字符串或一个包含多个字符串的列表
            top_n: 每个query搜索的top_n个结果
        Returns:
            result_list: 一个包含n个字符串的列表，每个字符串表示一个query的搜索结果
        '''
        # 如果query为空，则返回'invalid query'
        if len(query) == 0:
            return 'invalid query'

        # 1. 如果 query 为列表，分别对每个query进行搜索
        if isinstance(query, list):
            # 创建一个结果列表，长度与输入query列表相同
            result_list = []
            valid_queries = []
            valid_indices = []
            
            # 遍历所有查询，记录有效查询及其索引
            for i, q in enumerate(query):
                if q == '':
                    result_list.append('invalid query')
                else:
                    valid_queries.append(q)
                    valid_indices.append(i)
            
            # 如果有有效查询，进行批量搜索
            if valid_queries:
                url = f'{self.config.search_url}/batch_search'
                data = {'query': valid_queries, 'top_n': top_n}
                response = requests.post(url, json=data)
                
                # 处理搜索结果
                for idx, item in zip(valid_indices, response.json()):
                    curr_result = ''
                    for line in item:
                        curr_result += f"{line['contents']}\n\n"
                    result_list.insert(idx, curr_result.strip())
            
            return result_list
        
        # 2. 如果 query 为单个字符串，则进行单个搜索
        
        url = f'{self.config.search_url}/batch_search'
        data = {'query': [query], 'top_n': top_n}
        response = requests.post(url, json=data)
        
        result_list = [
            "\n\n".join(line['contents'] for line in item).strip()
            for item in response.json()
        ]
        return result_list

    @retry(max=5, sleep=1)
    def search(self, query: str):
        if query == '':
            return 'invalid query'

        url = f'{self.config.search_url}/search'
        data = {'query': query, 'top_n': 5}
        response = requests.post(url, json=data)
        retrieval_text = ''
        for line in response.json():
            retrieval_text += f"{line['contents']}\n\n"
        retrieval_text = retrieval_text.strip()
        return retrieval_text

    def extract_search_content(self, text: str) -> str:
        try:
            start_tag = '<query>'
            end_tag = '</query>'
            end_pos = text.rindex(end_tag)
            start_pos = text.rindex(start_tag, 0, end_pos)
            return text[start_pos + len(start_tag):end_pos].strip()
        except ValueError:
            return ""

    @torch.no_grad()
    def generate_sequences(self, prompts: DataProto, **kwargs) -> DataProto:
        '''
        parquet 文件 
        → RLHFDataset.__getitem__() 
        → DataLoader(collate_fn) 
        → ray_trainer.py 训练循环 
        → vllm_rollout.py 的 generate_sequences()
        '''
        
        # 从prompts.meta_info中获取max_time参数，默认为无限制(-1)
        max_time = prompts.meta_info.get('max_time', -1)  # 默认值-1表示无限制
        print("*" * 100)
        print(f"max_time:{max_time}")

        # rebuild vllm cache engine
        if self.config.free_cache_engine:
            self.inference_engine.init_cache_engine()

        ori_input_ids = prompts.batch['input_ids']  # (bs, prompt_length)

        # left-padded attention_mask
        attention_mask = prompts.batch['attention_mask']
        position_ids = prompts.batch['position_ids']

        # used to construct attention_mask
        eos_token_id = prompts.meta_info['eos_token_id']

        batch_size = ori_input_ids.size(0)

        idx_list = []
        # parse idx from torch.Tensor to List[List[str]]
        for i in range(batch_size):
            idx_list.append(_pre_process_inputs(self.pad_token_id, ori_input_ids[i]))

        do_sample = prompts.meta_info.get('do_sample', True)
        if not do_sample:
            kwargs = {
                'best_of': 1,
                'top_p': 1.0,
                'top_k': -1,
                'min_p': 0.0,
                'temperature': 0,
                'n': 1  # if greedy, only 1 response
            }

        with self.update_sampling_params(**kwargs):
            # prepare n copies for each input
            # 为每个输入准备 n 个副本（n 是生成的样本数量），存储在 curr_inputs 中。
            curr_inputs = []
            for input_ids in idx_list:
                for _ in range(self.sampling_params.n):
                    curr_inputs.append(input_ids.copy())
            #  保存输入的初始副本，用于后续参考。
            init_inputs = [ids.copy() for ids in curr_inputs]
            
            # track the status of each input
            # 跟踪每个输入允许生成的最大标记数。
            curr_max_tokens = [self.sampling_params.max_tokens] * len(curr_inputs)
            # 跟踪当前活跃的（尚未完成生成的）输入索引。
            active_indices = list(range(len(curr_inputs)))
            # 记录每个样本的检索次数
            retrieval_counts = [0] * len(curr_inputs)
            # 保存各样本的搜索结果
            search_documents = [[] for _ in range(len(curr_inputs))]

            # collect the result mask of each rollout
            #  为每个输入创建结果掩码列表，用于标记哪些部分是模型生成的（值为1），哪些部分是搜索结果（值为0）
            result_mask_list = [[] for _ in range(len(curr_inputs))]

            # generate until all inputs are finished
            while active_indices:
                # only process the active inputs
                active_inputs = [curr_inputs[i] for i in active_indices]
                active_max_tokens = [curr_max_tokens[i] for i in active_indices]

                # generate in batch, according to active max tokens
                # detokenize=True：运行过程边生成边解码，方便与 stop 标记进行比对
                with self.update_sampling_params(n=1, stop=['</query>', '</documents_refine>'], max_tokens=max(active_max_tokens), detokenize=True):
                    outputs = self.inference_engine.generate(
                        prompts=None,
                        sampling_params=self.sampling_params,
                        prompt_token_ids=active_inputs,
                        use_tqdm=True
                    )

                # collect the queries to search
                search_queries = []
                search_indices = []

                # process each output
                new_active_indices = []
                for i, idx in enumerate(active_indices):
                    
                    output_ids = outputs[0][i].tolist()
                    # 找到第一个 EOS 标记（结束标记）的位置
                    if self.tokenizer.eos_token_id in output_ids:
                        first_eos_idx = output_ids.index(self.tokenizer.eos_token_id)
                    else:
                        first_eos_idx = len(output_ids)
                    
                    # 找到第一个 PAD 标记（填充标记）的位置
                    if self.tokenizer.pad_token_id in output_ids:
                        first_pad_idx = output_ids.index(self.tokenizer.pad_token_id)
                    else:
                        first_pad_idx = len(output_ids)
                    
                    finish_reason = outputs[2][i]
                    stop_reason = outputs[3][i]

                    # 第一部分：当发现查询标签时，提取查询但不立即增加检索计数
                    if finish_reason == 'stop' and isinstance(stop_reason, str):
                        if '</query>' in stop_reason:
                            # need to search
                            # 检查是否达到最大检索次数
                            if max_time > 0 and retrieval_counts[idx] >= max_time:
                                # 达到最大检索次数，停止生成，但不进行检索
                                # 只保留已生成的部分
                                output_ids = output_ids[:first_pad_idx]
                                curr_inputs[idx] += output_ids
                                result_mask_list[idx] += [1] * len(output_ids)
                                assert len(curr_inputs[idx]) - len(init_inputs[idx]) == len(result_mask_list[idx]), f"Length mismatch 0: {len(curr_inputs[idx])} vs {len(init_inputs[idx])}, {len(result_mask_list[idx])}"
                                # 不将该索引添加到new_active_indices，表示停止生成
                            else:
                                # 未达到最大检索次数，正常进行检索
                                ## truncate from the first pad token
                                output_ids = output_ids[:first_pad_idx]
                                output_str = self.tokenizer.decode(output_ids)
                                ## process the search
                                search_content = self.extract_search_content(output_str)
                                search_queries.append(search_content)
                                search_indices.append(idx)
                                new_active_indices.append(idx)

                                ## update the current input
                                curr_inputs[idx] += output_ids
                                result_mask_list[idx] += [1] * len(output_ids)
                                retrieval_counts[idx] += 1
                                # 不在这里增加检索次数计数，移到下方搜索结果处理部分
                                assert len(curr_inputs[idx]) - len(init_inputs[idx]) == len(result_mask_list[idx]), f"Length mismatch 1: {len(curr_inputs[idx])} vs {len(init_inputs[idx])}, {len(result_mask_list[idx])}"

                        elif '</documents_refine>' in stop_reason:
                            output_ids = output_ids[:first_pad_idx]

                            # 检查结果掩码中是否有0，如果有则删除最后一段连续的0及对应内容
                            if 0 in result_mask_list[idx]:
                                # 获取当前生成的完整token序列和掩码
                                full_tokens = curr_inputs[idx][len(init_inputs[idx]):]
                                full_mask = result_mask_list[idx]
                                
                                # 计算<documents_refine>标记的长度
                                relevant_info_tokens = self.tokenizer.encode("<documents_refine>")
                                relevant_info_length = len(relevant_info_tokens)
                                #
                                # 找到最后一段连续的0的起始和结束位置
                                last_zero_start = -1
                                last_zero_end = -1
                                
                                # 从后向前查找第一个0
                                for i in range(len(full_mask)-1, -1, -1):
                                    if full_mask[i] == 0:
                                        last_zero_end = i + 1
                                        break
                                
                                # 如果找到0，继续向前查找连续的0序列的起始位置
                                if last_zero_end != -1:
                                    last_zero_start = last_zero_end
                                    for i in range(last_zero_end-1, -1, -1):
                                        if full_mask[i] != 0:
                                            last_zero_start = i + 1
                                            break
                                        if i == 0:
                                            last_zero_start = 0
                                
                                # 只有当0序列长度大于<documents_refine>标记长度时才删除
                                if (last_zero_start != -1 and last_zero_end != -1 \
                                    and last_zero_end - last_zero_start > relevant_info_length):
                                    
                                    last_zero_end = max(last_zero_end - relevant_info_length, 0)

                                    # 计算需要删除的token数量
                                    tokens_to_remove = last_zero_end - last_zero_start

                                    # 更新输入和掩码
                                    curr_inputs[idx] = init_inputs[idx] + full_tokens[:last_zero_start] + full_tokens[last_zero_end:]
                                    result_mask_list[idx] = full_mask[:last_zero_start] + full_mask[last_zero_end:]
                                    
                                    # 打印删除的内容
                                    removed_tokens = full_tokens[last_zero_start:last_zero_end]
                                    removed_text = self.tokenizer.decode(removed_tokens)

                            
                            curr_inputs[idx] += output_ids
                            result_mask_list[idx] += [1] * len(output_ids)

                            # 最终验证
                            generated_length = len(curr_inputs[idx]) - len(init_inputs[idx])
                            assert len(result_mask_list[idx]) == generated_length, \
                                f"Final length mismatch: mask={len(result_mask_list[idx])}, generated={generated_length}"
                            new_active_indices.append(idx)
                    elif finish_reason == 'stop' and stop_reason == None:
                        # output eos, indicating finished; truncate from the first eos token
                        output_ids = output_ids[:first_eos_idx+1]
                        curr_inputs[idx] += output_ids
                        result_mask_list[idx] += [1] * len(output_ids)
                        assert len(curr_inputs[idx]) - len(init_inputs[idx]) == len(result_mask_list[idx]), f"Length mismatch 4: {len(curr_inputs[idx])} vs {len(init_inputs[idx])}, {len(result_mask_list[idx])}"
                    elif finish_reason == 'stop' and stop_reason == self.tokenizer.pad_token_id:
                        # for instruction model, there is a chance that the end is endoftext, not im_end, this case needs special handling
                        output_ids = output_ids[:first_pad_idx+1]
                        curr_inputs[idx] += output_ids
                        result_mask_list[idx] += [1] * len(output_ids)
                        assert len(curr_inputs[idx]) - len(init_inputs[idx]) == len(result_mask_list[idx]), f"Length mismatch 5: {len(curr_inputs[idx])} vs {len(init_inputs[idx])}, {len(result_mask_list[idx])}"
                    elif finish_reason == 'length':
                        # output is too long
                        curr_inputs[idx] += output_ids
                        result_mask_list[idx] += [1] * len(output_ids)
                        assert len(curr_inputs[idx]) - len(init_inputs[idx]) == len(result_mask_list[idx]), f"Length mismatch 6: {len(curr_inputs[idx])} vs {len(init_inputs[idx])}, {len(result_mask_list[idx])}"
                        
                # batch process the search requests
                if search_queries:
                    search_results = self.batch_search(search_queries)
                    
                    # 添加搜索结果到输入并保存搜索文档
                    for idx, result in zip(search_indices, search_results):
                        search_documents[idx].append(result)
                        if result == "":
                            print(f"search : retrieval_counts: {retrieval_counts[idx]}, curr_inputs[idx]: {self.tokenizer.decode(curr_inputs[idx])}")
                        
                        output_ids = self.tokenizer.encode(f" <documents>\n{result}\n</documents> <documents_refine>")
                        curr_inputs[idx] += output_ids
                        result_mask_list[idx] += [0] * len(output_ids)
                        assert len(curr_inputs[idx]) - len(init_inputs[idx]) == len(result_mask_list[idx]), f"Length search_doc mismatch 7: {len(curr_inputs[idx])} vs {len(init_inputs[idx])}, {len(result_mask_list[idx])}"

                length_checked_active_indices = []
                for idx in active_indices:
                    # 验证输入长度与掩码长度一致
                    assert len(curr_inputs[idx]) - len(init_inputs[idx]) == len(result_mask_list[idx]), f"8 curr_inputs: {len(curr_inputs[idx])}, init_inputs: {len(init_inputs[idx])}, result_mask_list: {len(result_mask_list[idx])}"
                    # 如果生成的响应长度超过了配置的最大响应长度，则截断
                    if len(curr_inputs[idx]) - len(init_inputs[idx]) >= self.config.response_length:
                        curr_inputs[idx] = init_inputs[idx] \
                            + curr_inputs[idx][len(init_inputs[idx]):len(init_inputs[idx])+self.config.response_length]
                        result_mask_list[idx] = result_mask_list[idx][:self.config.response_length]
                    # 如果未超过最大长度且需要继续生成，则更新 curr_max_tokens 和活跃索引列表
                    else:
                        curr_max_tokens[idx] = self.config.response_length - len(curr_inputs[idx]) + len(init_inputs[idx])
                        if idx in new_active_indices:
                            length_checked_active_indices.append(idx)
                    
                    # 验证retrievel_counts和search_documents的长度
                    assert retrieval_counts[idx] == len(search_documents[idx]), f"9 retrieval_counts: {retrieval_counts[idx]}, search_documents: {len(search_documents[idx])}, search_documents: {search_documents[idx]}"
                active_indices = length_checked_active_indices

            output_ids_list = []
            # collect the results
            # 收集所有输入的生成结果
            final_retrieval_counts = []
            final_search_documents = []
            for i, input_ids in enumerate(idx_list):
                for j in range(self.sampling_params.n):
                    idx = i * self.sampling_params.n + j
                    input_len = len(input_ids)
                    output_ids_list.append(curr_inputs[idx][input_len:])
                    final_retrieval_counts.append(retrieval_counts[idx])
                    final_search_documents.append(search_documents[idx])

        # 格式化和处理最终响应
        response_list = []
        result_mask_list_padded = []
        for output_ids, result_mask in zip(output_ids_list, result_mask_list):
            assert len(output_ids) == len(result_mask), f"output_ids: {len(output_ids)}, result_mask: {len(result_mask)}"
            
            # 处理response
            response = torch.tensor(output_ids, device=ori_input_ids.device)
            response = pad_sequence_to_length(response, self.config.response_length, self.pad_token_id)
            
            # 处理result_mask
            result_mask = torch.tensor(result_mask, device=ori_input_ids.device)
            result_mask = pad_sequence_to_length(result_mask, self.config.response_length, 0)
            
            response_list.append(response)
            result_mask_list_padded.append(result_mask)
            # search_docs_list.append(docs_tensor)

        response = torch.stack(response_list, dim=0)
        result_mask = torch.stack(result_mask_list_padded, dim=0)

        if self.config.n > 1 and do_sample:
            ori_input_ids = ori_input_ids.repeat_interleave(self.config.n, dim=0)
            attention_mask = attention_mask.repeat_interleave(self.config.n, dim=0)
            position_ids = position_ids.repeat_interleave(self.config.n, dim=0)
            batch_size = batch_size * self.config.n
            
        # 将原始输入和响应连接起来创建完整序列
        seq = torch.cat([ori_input_ids, response], dim=-1)

        response_length = response.size(1)
        delta_position_id = torch.arange(1, response_length + 1, device=position_ids.device)
        delta_position_id = delta_position_id.unsqueeze(0).repeat(batch_size, 1)

        # TODO(sgm): fix position_ids on right_pad
        # prompt: left pad + response: right pad
        # attention_mask: [0,0,0,0,1,1,1,1, | 1,1,1,0,0,0,0,0]
        # position_ids:   [0,0,0,0,0,1,2,3, | 4,5,6,7,8,9,10,11]
        # 计算响应的位置ID并与原始位置ID连接
        response_position_ids = position_ids[:, -1:] + delta_position_id
        position_ids = torch.cat([position_ids, response_position_ids], dim=-1)
        response_attention_mask = get_eos_mask(response_id=response, eos_token=eos_token_id, dtype=attention_mask.dtype)
        attention_mask = torch.cat((attention_mask, response_attention_mask), dim=-1)

        # result mask: result part is 0, other part is 1
        # 计算损失掩码（结合结果掩码和注意力掩码）
        loss_mask = result_mask * response_attention_mask

        # 修改部分：只保留每个样本的最后一次检索内容
        for i in range(len(final_retrieval_counts)):
            # 只保留最后一次检索的文档
            if final_retrieval_counts[i] > 0:
                # 如果有检索，只保留最后一个文档
                final_search_documents[i] = [final_search_documents[i][-1]]

        # # 将检索次数转换为张量
        retrieval_count_tensor = torch.tensor(final_retrieval_counts, device=ori_input_ids.device)

        MAX_DOC_LENGTH = self.config.response_length
        encoded_docs = []
        for docs in final_search_documents:
            if not docs:
                doc_tensor = torch.full((1,), self.pad_token_id, dtype=torch.long, device=ori_input_ids.device)
            else:
                combined_doc = docs[0]
                encoded = self.tokenizer.encode(combined_doc)[:MAX_DOC_LENGTH]  # 限长
                doc_tensor = torch.tensor(encoded, dtype=torch.long, device=ori_input_ids.device)
            encoded_docs.append(doc_tensor)

        search_docs_tensor_list = [pad_sequence_to_length(doc_tensor, MAX_DOC_LENGTH, self.pad_token_id) for doc_tensor in encoded_docs]

        search_docs_tensor = torch.stack(search_docs_tensor_list, dim=0)

        batch = TensorDict({
            'prompts': ori_input_ids,
            'responses': response,  # 完整的响应序列，全部的迭代的response
            'input_ids': seq,  # here input_ids become the whole sentences完整序列（提示+响应）
            'attention_mask': attention_mask,
            'loss_mask': loss_mask, # 损失掩码，用于训练时忽略搜索结果部分
            'position_ids': position_ids,
            'retrieval_counts': retrieval_count_tensor,  # 添加检索次数到返回结果中
            'search_documents': search_docs_tensor  # 添加转换后的搜索文档token IDs
        }, batch_size=batch_size)

        # free vllm cache engine
        if self.config.free_cache_engine:
            self.inference_engine.free_cache_engine()

        return DataProto(batch=batch)


class vLLMRolloutWithSearch2(vLLMRollout):
    def __init__(self, actor_module: nn.Module, config: DictConfig, tokenizer, model_hf_config, **kwargs):
        super().__init__(actor_module, config, tokenizer, model_hf_config, **kwargs)
        self.tokenizer = tokenizer

        # 找到所有以 > 开头的 token
        self.org_stop_str = '>'
        self.gt_tokens, self.reversed_gt_tokens = self.find_gt_prefix_tokens(self.tokenizer, prefix=self.org_stop_str)
        
        # 定义 stop token ids 只要以 > 开头的 token 都算 stop token
        self.stop_token_ids = [item for item in self.reversed_gt_tokens.keys()]

        # 预计算停止 str 对应的所有 token 的 ID（每个 str 对应一个 token 列表，并转换为一个 token_id 列表）
        self.stop_strs = ["</search>"]

        print("触发 vLLMRolloutWithSearchA2.__init__ ")

    @retry(max=5, sleep=1)
    def batch_search(self, query: Union[str, List[str]], top_n=5) -> List[str]:
        '''
        批量搜索: 根据 n 个 query 搜索 n * top_n 个结果
        Args:
            query: 一个字符串或一个包含多个字符串的列表
            top_n: 每个query搜索的top_n个结果
        Returns:
            result_list: 一个包含n个字符串的列表，每个字符串表示一个query的搜索结果
        '''
        # 如果query为空，则返回'invalid query'
        if len(query) == 0:
            return 'invalid query'

        # 1. 如果 query 为列表，分别对每个query进行搜索
        if isinstance(query, list):
            # 创建一个结果列表，长度与输入query列表相同
            result_list = []
            valid_queries = []
            valid_indices = []
            
            # 遍历所有查询，记录有效查询及其索引
            for i, q in enumerate(query):
                if q == '':
                    result_list.append('invalid query')
                else:
                    valid_queries.append(q)
                    valid_indices.append(i)
            
            # 如果有有效查询，进行批量搜索
            if valid_queries:
                url = f'{self.config.search_url}/batch_search'
                data = {'query': valid_queries, 'top_n': top_n}
                response = requests.post(url, json=data)
                
                # 处理搜索结果
                for idx, item in zip(valid_indices, response.json()):
                    curr_result = ''
                    for line in item:
                        curr_result += f"{line['contents']}\n\n"
                    result_list.insert(idx, curr_result.strip())
            
            return result_list
        
        # 2. 如果 query 为单个字符串，则进行单个搜索
        
        url = f'{self.config.search_url}/batch_search'
        data = {'query': [query], 'top_n': top_n}
        response = requests.post(url, json=data)
        
        result_list = [
            "\n\n".join(line['contents'] for line in item).strip()
            for item in response.json()
        ]
        return result_list

    @retry(max=5, sleep=1)
    def serper_search(self, query: Union[str, List[str]], top_n: int=10):
        """如果是批量检索，返回的结果是 list 否则就是直接一个 item map"""

        if isinstance(query, str):
            texts = [query]

        url = "https://google.serper.dev/search"

        payload = json.dumps([{
            "q": f"{text}",
            "location": "United States",
            "num": top_n
        } for text in texts])
        headers = {
            'X-API-KEY': '3fa11d6a07bf67b82798ebefb2787ad9e84f21fd',
            'Content-Type': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload)
        response_json = json.loads(response.text)

        snippets_list = []
        for item in response_json:
            results = item.get('organic', [])
            snippets = [result['snippet'] for result in results]
            snippets = "\n\n".join(snippets)
            snippets_list.append(snippets)
        return snippets_list
    
    

    @retry(max=5, sleep=1)
    def search(self, query: str):
        if query == '':
            return 'invalid query'

        url = f'{self.config.search_url}/search'
        data = {'query': query, 'top_n': 5}
        response = requests.post(url, json=data)
        retrieval_text = ''
        for line in response.json():
            retrieval_text += f"{line['contents']}\n\n"
        retrieval_text = retrieval_text.strip()
        return retrieval_text

    def extract_search_content(self, text: str) -> str:
        try:
            start_tag = '<search>'
            end_tag = '</search>'
            end_pos = text.rindex(end_tag)
            start_pos = text.rindex(start_tag, 0, end_pos)
            return text[start_pos + len(start_tag):end_pos].strip()
        except ValueError:
            return ""

    def find_gt_prefix_tokens(self, tokenizer, prefix='>'):
        '''
        找到所有以 prefix 开头的 token
        Returns:
            results: 一个包含所有匹配 token 的 map，每个元素是一个 {token_str: token_id, ...} 元组
        '''
        results = {}
        reversed_results = {}
        vocab_size = tokenizer.vocab_size if hasattr(tokenizer, 'vocab_size') else len(tokenizer)

        for token_id in range(vocab_size):
            token_str = tokenizer.decode([token_id], clean_up_tokenization_spaces=False).strip()
            if token_str.startswith(prefix):
                results[token_str] = token_id
                reversed_results[token_id] = token_str
        return results, reversed_results

    def check_sequence_match(self, output_ids):
        # 1. 替换 stop_str 为 org_stop_str (把 > 后的杂质去掉方便进行匹配)
        cur_stop_str = self.reversed_gt_tokens.get(output_ids[-1], None)
        output_str = self.tokenizer.decode(output_ids)
        output_str = output_str.replace(cur_stop_str, self.org_stop_str)
        output_str = output_str.replace("\n", "").strip() # \n 无法被 str 保存，因此单独处理

        # 2. 检测是否以任何停止标记结尾
        for stop_str in self.stop_strs:
            if output_str.endswith(stop_str):
                return True, stop_str
        return False, None
    
    def stop_on_sequence_processor(stop_sequence: list[int]):  
        """创建一个在遇到特定连续序列时停止的 logits processor"""  
        def processor(past_tokens: list[int], logits: torch.Tensor) -> torch.Tensor:  
            # 检查是否匹配停止序列  
            if len(past_tokens) >= len(stop_sequence):  
                # 检查最后 N 个 tokens 是否匹配停止序列  
                recent_tokens = past_tokens[-len(stop_sequence):]  
                if recent_tokens == stop_sequence:  
                    # 如果匹配，将所有 logits 设为负无穷，强制停止  
                    logits.fill_(float('-inf'))  
                    # 或者可以设置 EOS token 的概率为最高  
                    # logits[eos_token_id] = float('inf')  
            
            return logits  
      
        return processor

    @torch.no_grad()
    def generate_sequences(self, prompts: DataProto, **kwargs) -> DataProto:
        '''
        所有数据的流动
        parquet 文件
        → RLHFDataset.__getitem__()
        → DataLoader(collate_fn)
        → ray_trainer.py 训练循环
        → vllm_rollout.py 的 generate_sequences()
        '''
        
        # 从prompts.meta_info中获取max_time参数，默认为无限制(-1)
        max_time = prompts.meta_info.get('max_time', -1)  # 默认值-1表示无限制
        # max_time = 0  # 默认值-1表示无限制
        print("*" * 100)
        print(f"max_time:{max_time}")

        # breakpoint()

        # rebuild vllm cache engine
        if self.config.free_cache_engine:
            self.inference_engine.init_cache_engine()

        ori_input_ids = prompts.batch['input_ids']  # (bs, prompt_length)

        # left-padded attention_mask
        attention_mask = prompts.batch['attention_mask']
        position_ids = prompts.batch['position_ids']

        # used to construct attention_mask
        eos_token_id = prompts.meta_info['eos_token_id']

        batch_size = ori_input_ids.size(0)

        idx_list = []
        # parse idx from torch.Tensor to List[List[str]]
        for i in range(batch_size):
            idx_list.append(_pre_process_inputs(self.pad_token_id, ori_input_ids[i]))

        do_sample = prompts.meta_info.get('do_sample', True)
        if not do_sample:
            kwargs = {
                'best_of': 1,
                'top_p': 1.0,
                'top_k': -1,
                'min_p': 0.0,
                'temperature': 0,
                'n': 1  # if greedy, only 1 response
            }

        with self.update_sampling_params(**kwargs):
            # prepare n copies for each input
            # 为每个输入准备 n 个副本（n 是生成的样本数量），存储在 curr_inputs 中。
            
            curr_inputs = []
            for input_ids in idx_list:
                for _ in range(self.sampling_params.n):
                    curr_inputs.append(input_ids.copy())
            #  保存输入的初始副本，用于后续参考（这里 ids 拷贝以后维度少了一维）
            init_inputs = [ids.copy() for ids in curr_inputs]
            
            # track the status of each input
            # 跟踪每个输入允许生成的最大标记数。
            curr_max_tokens = [self.sampling_params.max_tokens] * len(curr_inputs)
            # 跟踪当前活跃的（尚未完成生成的）输入索引。
            active_indices = list(range(len(curr_inputs)))
            # 记录每个样本的检索次数
            retrieval_counts = [0] * len(curr_inputs)
            # 保存各样本的搜索结果
            search_documents = [[] for _ in range(len(curr_inputs))]

            # collect the result mask of each rollout
            #  为每个输入创建结果掩码列表，用于标记哪些部分是模型生成的（值为1），哪些部分是搜索结果（值为0）
            result_mask_list = [[] for _ in range(len(curr_inputs))]

            # generate until all inputs are finished
            while active_indices:
                # only process the active inputs
                active_inputs = [curr_inputs[i] for i in active_indices]
                active_max_tokens = [curr_max_tokens[i] for i in active_indices]

                # generate in batch, according to active max tokens
                with self.update_sampling_params(n=1, stop_token_ids=self.stop_token_ids, max_tokens=max(active_max_tokens)): # 计算效率低下区域
                    outputs = self.inference_engine.generate(
                        prompts=None,
                        sampling_params=self.sampling_params,
                        prompt_token_ids=active_inputs,
                        use_tqdm=True
                    )
                
                # collect the queries to search
                search_queries = []
                search_indices = []

                # process each output
                new_active_indices = []
                for i, idx in enumerate(active_indices):
                    # 提取当前输出的 token 列表（截止到上一次暂停中间所产生的 token id）
                    output_ids = outputs[0][i].tolist()

                    # 找到第一个 EOS 标记（结束标记）的位置
                    if self.tokenizer.eos_token_id in output_ids:
                        first_eos_idx = output_ids.index(self.tokenizer.eos_token_id)
                    else:
                        first_eos_idx = len(output_ids)
                    
                    # 找到第一个 PAD 标记（填充标记）的位置
                    if self.tokenizer.pad_token_id in output_ids:
                        first_pad_idx = output_ids.index(self.tokenizer.pad_token_id)
                    else:
                        first_pad_idx = len(output_ids)
                    
                    finish_reason = outputs[2][i]
                    stop_reason = outputs[3][i]

                    # 第一部分：当发现查询标签时，提取查询但不立即增加检索计数
                    if finish_reason == 'stop' and isinstance(stop_reason, int) and stop_reason in self.stop_token_ids:
                        is_match, matched_str = self.check_sequence_match(output_ids[:first_pad_idx])
                        if is_match and matched_str == '</search>':
                            # need to search
                            # 检查是否达到最大检索次数
                            if max_time > 0 and retrieval_counts[idx] >= max_time:
                                # 达到最大检索次数，停止生成，但不进行检索
                                # 只保留已生成的部分
                                output_ids = output_ids[:first_pad_idx]
                                curr_inputs[idx] += output_ids
                                result_mask_list[idx] += [1] * len(output_ids)
                                assert len(curr_inputs[idx]) - len(init_inputs[idx]) == len(result_mask_list[idx]), f"Length mismatch 0: {len(curr_inputs[idx])} vs {len(init_inputs[idx])}, {len(result_mask_list[idx])}"
                                # 不将该索引添加到new_active_indices，表示停止生成
                            else:
                                # 未达到最大检索次数，正常进行检索
                                ## 找到输出结果中的有效输出（排除填充标记）
                                output_ids = output_ids[:first_pad_idx]
                                output_str = self.tokenizer.decode(curr_inputs[idx] + output_ids)

                                ## 将输入和输出拼接到一起搜索 query 内容
                                search_content = self.extract_search_content(output_str)
                                search_queries.append(search_content)
                                search_indices.append(idx)
                                new_active_indices.append(idx)

                                ## 更新 curr_inputs 为最新内容
                                curr_inputs[idx] += output_ids
                                result_mask_list[idx] += [1] * len(output_ids)
                                retrieval_counts[idx] += 1

                                assert len(curr_inputs[idx]) - len(init_inputs[idx]) == len(result_mask_list[idx]), f"Length mismatch 1: {len(curr_inputs[idx])} vs {len(init_inputs[idx])}, {len(result_mask_list[idx])}"
                    
                        else:
                            # 如果没有匹配的停止标记，继续生成
                            ## truncate from the first pad token
                            output_ids = output_ids[:first_pad_idx]
                            ## process the search
                            new_active_indices.append(idx)

                            ## update the current input
                            curr_inputs[idx] += output_ids
                            result_mask_list[idx] += [1] * len(output_ids)
                            # 不在这里增加检索次数计数，移到下方搜索结果处理部分
                            assert len(curr_inputs[idx]) - len(init_inputs[idx]) == len(result_mask_list[idx]), f"Length mismatch 1: {len(curr_inputs[idx])} vs {len(init_inputs[idx])}, {len(result_mask_list[idx])}"
                    
                    elif finish_reason == 'stop' and stop_reason == None:
                        # output eos, indicating finished; truncate from the first eos token
                        output_ids = output_ids[:first_eos_idx+1]
                        curr_inputs[idx] += output_ids
                        result_mask_list[idx] += [1] * len(output_ids)
                        assert len(curr_inputs[idx]) - len(init_inputs[idx]) == len(result_mask_list[idx]), f"Length mismatch 4: {len(curr_inputs[idx])} vs {len(init_inputs[idx])}, {len(result_mask_list[idx])}"
                    elif finish_reason == 'stop' and stop_reason == self.tokenizer.pad_token_id:
                        # for instruction model, there is a chance that the end is endoftext, not im_end, this case needs special handling
                        output_ids = output_ids[:first_pad_idx+1]
                        curr_inputs[idx] += output_ids
                        result_mask_list[idx] += [1] * len(output_ids)
                        assert len(curr_inputs[idx]) - len(init_inputs[idx]) == len(result_mask_list[idx]), f"Length mismatch 5: {len(curr_inputs[idx])} vs {len(init_inputs[idx])}, {len(result_mask_list[idx])}"
                    elif finish_reason == 'length':
                        # output is too long
                        curr_inputs[idx] += output_ids
                        result_mask_list[idx] += [1] * len(output_ids)
                        assert len(curr_inputs[idx]) - len(init_inputs[idx]) == len(result_mask_list[idx]), f"Length mismatch 6: {len(curr_inputs[idx])} vs {len(init_inputs[idx])}, {len(result_mask_list[idx])}"
                        
                # batch process the search requests
                if search_queries:
                    # search_results = self.batch_search(search_queries)
                    search_results = self.serper_search(search_queries)
                    
                    # 添加搜索结果到输入并保存搜索文档
                    for idx, result in zip(search_indices, search_results):
                        search_documents[idx].append(result)
                        if result == "":
                            print(f"search : retrieval_counts: {retrieval_counts[idx]}, curr_inputs[idx]: {self.tokenizer.decode(curr_inputs[idx])}")
                        
                        output_ids = self.tokenizer.encode(f" <result>\n{result}\n</result> ")
                        curr_inputs[idx] += output_ids
                        result_mask_list[idx] += [0] * len(output_ids)
                        assert len(curr_inputs[idx]) - len(init_inputs[idx]) == len(result_mask_list[idx]), f"Length search_doc mismatch 7: {len(curr_inputs[idx])} vs {len(init_inputs[idx])}, {len(result_mask_list[idx])}"

                # 更新最大输出长度限制
                length_checked_active_indices = []
                for idx in active_indices:
                    # 验证输入长度与掩码长度一致
                    assert len(curr_inputs[idx]) - len(init_inputs[idx]) == len(result_mask_list[idx]), f"8 curr_inputs: {len(curr_inputs[idx])}, init_inputs: {len(init_inputs[idx])}, result_mask_list: {len(result_mask_list[idx])}"
                    # 如果生成的响应长度超过了配置的最大响应长度，则截断
                    if len(curr_inputs[idx]) - len(init_inputs[idx]) >= self.config.response_length:
                        curr_inputs[idx] = init_inputs[idx] \
                            + curr_inputs[idx][len(init_inputs[idx]):len(init_inputs[idx])+self.config.response_length]
                        result_mask_list[idx] = result_mask_list[idx][:self.config.response_length]
                    # 如果未超过最大长度且需要继续生成，则更新 curr_max_tokens 和活跃索引列表
                    else:
                        curr_max_tokens[idx] = self.config.response_length - len(curr_inputs[idx]) + len(init_inputs[idx])
                        if idx in new_active_indices:
                            length_checked_active_indices.append(idx)
                    
                    # 验证retrievel_counts和search_documents的长度
                    assert retrieval_counts[idx] == len(search_documents[idx]), f"9 retrieval_counts: {retrieval_counts[idx]}, search_documents: {len(search_documents[idx])}, search_documents: {search_documents[idx]}"
                active_indices = length_checked_active_indices

            output_ids_list = []
            # collect the results
            # 收集所有输入的生成结果
            final_retrieval_counts = []
            final_search_documents = []
            for i, input_ids in enumerate(idx_list):
                for j in range(self.sampling_params.n):
                    idx = i * self.sampling_params.n + j
                    input_len = len(input_ids)
                    output_ids_list.append(curr_inputs[idx][input_len:])
                    final_retrieval_counts.append(retrieval_counts[idx])
                    final_search_documents.append(search_documents[idx])

        # 格式化和处理最终响应
        response_list = []
        result_mask_list_padded = []
        for output_ids, result_mask in zip(output_ids_list, result_mask_list):
            assert len(output_ids) == len(result_mask), f"output_ids: {len(output_ids)}, result_mask: {len(result_mask)}"
            
            # 处理response
            response = torch.tensor(output_ids, device=ori_input_ids.device, dtype=torch.int64)
            response = pad_sequence_to_length(response, self.config.response_length, self.pad_token_id)
            
            # 处理result_mask
            result_mask = torch.tensor(result_mask, device=ori_input_ids.device)
            result_mask = pad_sequence_to_length(result_mask, self.config.response_length, 0)
            
            response_list.append(response)
            result_mask_list_padded.append(result_mask)
            # search_docs_list.append(docs_tensor)

        response = torch.stack(response_list, dim=0)
        result_mask = torch.stack(result_mask_list_padded, dim=0)

        if self.config.n > 1 and do_sample:
            ori_input_ids = ori_input_ids.repeat_interleave(self.config.n, dim=0)
            attention_mask = attention_mask.repeat_interleave(self.config.n, dim=0)
            position_ids = position_ids.repeat_interleave(self.config.n, dim=0)
            batch_size = batch_size * self.config.n
            
        # 将原始输入和响应连接起来创建完整序列
        seq = torch.cat([ori_input_ids, response], dim=-1)

        response_length = response.size(1)
        delta_position_id = torch.arange(1, response_length + 1, device=position_ids.device)
        delta_position_id = delta_position_id.unsqueeze(0).repeat(batch_size, 1)

        # TODO(sgm): fix position_ids on right_pad
        # prompt: left pad + response: right pad
        # attention_mask: [0,0,0,0,1,1,1,1, | 1,1,1,0,0,0,0,0]
        # position_ids:   [0,0,0,0,0,1,2,3, | 4,5,6,7,8,9,10,11]
        # 计算响应的位置ID并与原始位置ID连接
        response_position_ids = position_ids[:, -1:] + delta_position_id
        position_ids = torch.cat([position_ids, response_position_ids], dim=-1)
        response_attention_mask = get_eos_mask(response_id=response, eos_token=eos_token_id, dtype=attention_mask.dtype)
        attention_mask = torch.cat((attention_mask, response_attention_mask), dim=-1)

        # result mask: result part is 0, other part is 1
        # 计算损失掩码（结合结果掩码和注意力掩码）
        loss_mask = result_mask * response_attention_mask

        # 修改部分：只保留每个样本的最后一次检索内容
        for i in range(len(final_retrieval_counts)):
            # 只保留最后一次检索的文档
            if final_retrieval_counts[i] > 0:
                # 如果有检索，只保留最后一个文档
                final_search_documents[i] = [final_search_documents[i][-1]]

        # # 将检索次数转换为张量
        retrieval_count_tensor = torch.tensor(final_retrieval_counts, device=ori_input_ids.device)

        MAX_DOC_LENGTH = self.config.response_length
        encoded_docs = []
        for docs in final_search_documents:
            if not docs:
                doc_tensor = torch.full((1,), self.pad_token_id, dtype=torch.long, device=ori_input_ids.device)
            else:
                combined_doc = docs[0]
                encoded = self.tokenizer.encode(combined_doc)[:MAX_DOC_LENGTH]  # 限长
                doc_tensor = torch.tensor(encoded, dtype=torch.long, device=ori_input_ids.device)
            encoded_docs.append(doc_tensor)

        search_docs_tensor_list = [pad_sequence_to_length(doc_tensor, MAX_DOC_LENGTH, self.pad_token_id) for doc_tensor in encoded_docs]

        search_docs_tensor = torch.stack(search_docs_tensor_list, dim=0)

        batch = TensorDict({
            'prompts': ori_input_ids,
            'responses': response,  # 完整的响应序列，全部的迭代的response
            'input_ids': seq,  # here input_ids become the whole sentences完整序列（提示+响应）
            'attention_mask': attention_mask,
            'loss_mask': loss_mask, # 损失掩码，用于训练时忽略搜索结果部分
            'position_ids': position_ids,
            'retrieval_counts': retrieval_count_tensor,  # 添加检索次数到返回结果中
            'search_documents': search_docs_tensor  # 添加转换后的搜索文档token IDs
        }, batch_size=batch_size)

        # free vllm cache engine
        if self.config.free_cache_engine:
            self.inference_engine.free_cache_engine()

        return DataProto(batch=batch)

class vLLMRolloutWithSearchAccelerate(vLLMRolloutWithSearch):
    def __init__(self, actor_module: nn.Module, config: DictConfig, tokenizer, model_hf_config, **kwargs):
        super().__init__(actor_module, config, tokenizer, model_hf_config, **kwargs)
        # self.tokenizer = tokenizer

        # 找到所有以 > 开头的 token
        self.org_stop_str = '>'
        self.gt_tokens, self.reversed_gt_tokens = self.find_gt_prefix_tokens(self.tokenizer, prefix=self.org_stop_str)
        
        # 定义 stop token ids 只要以 > 开头的 token 都算 stop token
        self.stop_token_ids = [item for item in self.reversed_gt_tokens.keys()]

        # 预计算停止 str 对应的所有 token 的 ID（每个 str 对应一个 token 列表，并转换为一个 token_id 列表）
        self.stop_strs = ["</query>", "</documents_refine>"]

        print("触发 vLLMRolloutWithSearchAccelerate.__init__ ")

    @retry(max=5, sleep=1)
    def serper_search(self, query: Union[str, List[str]], top_n: int=10):
        print("perser search len:", len(query))
        """如果是批量检索，返回的结果是 list 否则就是直接一个 item map"""

        if isinstance(query, str):
            texts = [query]
        else:
            texts = query

        url = "https://google.serper.dev/search"

        payload = json.dumps([{
            "q": f"{text}",
            "location": "United States",
            "num": top_n
        } for text in texts])
        headers = {
            'X-API-KEY': '3fa11d6a07bf67b82798ebefb2787ad9e84f21fd',
            'Content-Type': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload)
        response_json = json.loads(response.text)

        snippets_list = []
        for item in response_json:
            results = item.get('organic', [])
            snippets = [result['snippet'] for result in results]
            snippets = "\n\n".join(snippets)
            snippets_list.append(snippets)
        return snippets_list
    
    def find_gt_prefix_tokens(self, tokenizer, prefix='>'):
        '''
        找到所有以 prefix 开头的 token
        Returns:
            results: 一个包含所有匹配 token 的 map，每个元素是一个 {token_str: token_id, ...} 元组
        '''
        results = {}
        reversed_results = {}
        vocab_size = tokenizer.vocab_size if hasattr(tokenizer, 'vocab_size') else len(tokenizer)

        for token_id in range(vocab_size):
            token_str = tokenizer.decode([token_id], clean_up_tokenization_spaces=False).strip()
            if token_str.startswith(prefix):
                results[token_str] = token_id
                reversed_results[token_id] = token_str
        return results, reversed_results

    def check_sequence_match(self, output_ids):
        # 遍历每个停止标记
        # for stop_str, stop_seq in self.stop_token_ids_map.items():
        #     stop_len = len(stop_seq)
        #     if len(output_ids) >= stop_len:
        #         if stop_seq == output_ids[-stop_len:]:
        #             final_stop_str = stop_str.replace("\n", "").strip()
        #             return True, final_stop_str
        # return False, None

        # 1. 替换 stop_str 为 org_stop_str (把 > 后的杂质去掉方便进行匹配)
        cur_stop_str = self.reversed_gt_tokens.get(output_ids[-1], None)
        output_str = self.tokenizer.decode(output_ids)
        output_str = output_str.replace(cur_stop_str, self.org_stop_str)
        output_str = output_str.replace("\n", "").strip() # \n 无法被 str 保存，因此单独处理

        # 2. 检测是否以任何停止标记结尾
        for stop_str in self.stop_strs:
            if output_str.endswith(stop_str):
                return True, stop_str
        return False, None
    
    def stop_on_sequence_processor(stop_sequence: list[int]):  
        """创建一个在遇到特定连续序列时停止的 logits processor"""  
        def processor(past_tokens: list[int], logits: torch.Tensor) -> torch.Tensor:  
            # 检查是否匹配停止序列  
            if len(past_tokens) >= len(stop_sequence):  
                # 检查最后 N 个 tokens 是否匹配停止序列  
                recent_tokens = past_tokens[-len(stop_sequence):]  
                if recent_tokens == stop_sequence:  
                    # 如果匹配，将所有 logits 设为负无穷，强制停止  
                    logits.fill_(float('-inf'))  
                    # 或者可以设置 EOS token 的概率为最高  
                    # logits[eos_token_id] = float('inf')  
            
            return logits  
      
        return processor

    @torch.no_grad()
    def generate_sequences(self, prompts: DataProto, **kwargs) -> DataProto:
        '''
        所有数据的流动
        parquet 文件
        → RLHFDataset.__getitem__()
        → DataLoader(collate_fn)
        → ray_trainer.py 训练循环
        → vllm_rollout.py 的 generate_sequences()
        '''
        
        # 从prompts.meta_info中获取max_time参数，默认为无限制(-1)
        max_time = prompts.meta_info.get('max_time', -1)  # 默认值-1表示无限制
        # max_time = 0  # 默认值-1表示无限制
        print("*" * 100)
        print(f"max_time:{max_time}")

        # breakpoint()

        # rebuild vllm cache engine
        if self.config.free_cache_engine:
            self.inference_engine.init_cache_engine()

        ori_input_ids = prompts.batch['input_ids']  # (bs, prompt_length)

        # left-padded attention_mask
        attention_mask = prompts.batch['attention_mask']
        position_ids = prompts.batch['position_ids']

        # used to construct attention_mask
        eos_token_id = prompts.meta_info['eos_token_id']

        batch_size = ori_input_ids.size(0)

        idx_list = []
        # parse idx from torch.Tensor to List[List[str]]
        for i in range(batch_size):
            idx_list.append(_pre_process_inputs(self.pad_token_id, ori_input_ids[i]))

        do_sample = prompts.meta_info.get('do_sample', True)
        if not do_sample:
            kwargs = {
                'best_of': 1,
                'top_p': 1.0,
                'top_k': -1,
                'min_p': 0.0,
                'temperature': 0,
                'n': 1  # if greedy, only 1 response
            }

        with self.update_sampling_params(**kwargs):
            # prepare n copies for each input
            # 为每个输入准备 n 个副本（n 是生成的样本数量），存储在 curr_inputs 中。
            
            curr_inputs = []
            for input_ids in idx_list:
                for _ in range(self.sampling_params.n):
                    curr_inputs.append(input_ids.copy())
            #  保存输入的初始副本，用于后续参考（这里 ids 拷贝以后维度少了一维）
            init_inputs = [ids.copy() for ids in curr_inputs]
            
            # track the status of each input
            # 跟踪每个输入允许生成的最大标记数。
            curr_max_tokens = [self.sampling_params.max_tokens] * len(curr_inputs)
            # 跟踪当前活跃的（尚未完成生成的）输入索引。
            active_indices = list(range(len(curr_inputs)))
            # 记录每个样本的检索次数
            retrieval_counts = [0] * len(curr_inputs)
            # 保存各样本的搜索结果
            search_documents = [[] for _ in range(len(curr_inputs))]

            # collect the result mask of each rollout
            #  为每个输入创建结果掩码列表，用于标记哪些部分是模型生成的（值为1），哪些部分是搜索结果（值为0）
            result_mask_list = [[] for _ in range(len(curr_inputs))]

            # generate until all inputs are finished
            while active_indices:
                # only process the active inputs
                active_inputs = [curr_inputs[i] for i in active_indices]
                active_max_tokens = [curr_max_tokens[i] for i in active_indices]

                # generate in batch, according to active max tokens
                with self.update_sampling_params(n=1, stop_token_ids=self.stop_token_ids, max_tokens=max(active_max_tokens)): # 计算效率低下区域
                    outputs = self.inference_engine.generate(
                        prompts=None,
                        sampling_params=self.sampling_params,
                        prompt_token_ids=active_inputs,
                        use_tqdm=True
                    )
                
                # collect the queries to search
                search_queries = []
                search_indices = []

                # process each output
                new_active_indices = []
                for i, idx in enumerate(active_indices):
                    # 提取当前输出的 token 列表（截止到上一次暂停中间所产生的 token id）
                    output_ids = outputs[0][i].tolist()

                    # 找到第一个 EOS 标记（结束标记）的位置
                    if self.tokenizer.eos_token_id in output_ids:
                        first_eos_idx = output_ids.index(self.tokenizer.eos_token_id)
                    else:
                        first_eos_idx = len(output_ids)
                    
                    # 找到第一个 PAD 标记（填充标记）的位置
                    if self.tokenizer.pad_token_id in output_ids:
                        first_pad_idx = output_ids.index(self.tokenizer.pad_token_id)
                    else:
                        first_pad_idx = len(output_ids)
                    
                    finish_reason = outputs[2][i]
                    stop_reason = outputs[3][i]

                    # 第一部分：当发现查询标签时，提取查询但不立即增加检索计数
                    if finish_reason == 'stop' and isinstance(stop_reason, int) and stop_reason in self.stop_token_ids:
                        is_match, matched_str = self.check_sequence_match(output_ids[:first_pad_idx])
                        if is_match and matched_str == '</query>':
                            # need to search
                            # 检查是否达到最大检索次数
                            if max_time > 0 and retrieval_counts[idx] >= max_time:
                                # 达到最大检索次数，停止生成，但不进行检索
                                # 只保留已生成的部分
                                output_ids = output_ids[:first_pad_idx]
                                curr_inputs[idx] += output_ids
                                result_mask_list[idx] += [1] * len(output_ids)
                                assert len(curr_inputs[idx]) - len(init_inputs[idx]) == len(result_mask_list[idx]), f"Length mismatch 0: {len(curr_inputs[idx])} vs {len(init_inputs[idx])}, {len(result_mask_list[idx])}"
                                # 不将该索引添加到new_active_indices，表示停止生成
                            else:
                                # 未达到最大检索次数，正常进行检索
                                ## 找到输出结果中的有效输出（排除填充标记）
                                output_ids = output_ids[:first_pad_idx]
                                output_str = self.tokenizer.decode(curr_inputs[idx] + output_ids)

                                ## 将输入和输出拼接到一起搜索 query 内容
                                search_content = self.extract_search_content(output_str)
                                search_queries.append(search_content)
                                search_indices.append(idx)
                                new_active_indices.append(idx)

                                ## 更新 curr_inputs 为最新内容
                                curr_inputs[idx] += output_ids
                                result_mask_list[idx] += [1] * len(output_ids)
                                retrieval_counts[idx] += 1

                                assert len(curr_inputs[idx]) - len(init_inputs[idx]) == len(result_mask_list[idx]), f"Length mismatch 1: {len(curr_inputs[idx])} vs {len(init_inputs[idx])}, {len(result_mask_list[idx])}"

                        elif is_match and matched_str == '</documents_refine>':
                            # 处理 <documents_refine> 标记
                            ## 找到输出结果中的有效输出（排除填充标记）
                            output_ids = output_ids[:first_pad_idx]

                            # 检查结果掩码中是否有0，如果有则删除最后一段连续的0及对应内容
                            if 0 in result_mask_list[idx]:
                                # 获取当前生成的完整token序列和掩码
                                full_tokens = curr_inputs[idx][len(init_inputs[idx]):]
                                # full_tokens = (curr_inputs[idx] + output_ids)[len(init_inputs[idx]):]
                                full_mask = result_mask_list[idx]
                                
                                # 计算 <documents_refine> 标记的长度
                                relevant_info_tokens = self.tokenizer.encode("<documents_refine>")
                                relevant_info_length = len(relevant_info_tokens)
                                
                                # 找到最后一段连续的0的起始和结束位置
                                last_zero_start = -1
                                last_zero_end = -1
                                
                                # 从后向前查找第一个0
                                for i in range(len(full_mask)-1, -1, -1):
                                    if full_mask[i] == 0:
                                        last_zero_end = i + 1
                                        break
                                
                                # 如果找到0，继续向前查找连续的0序列的起始位置
                                if last_zero_end != -1:
                                    last_zero_start = last_zero_end
                                    for i in range(last_zero_end-1, -1, -1):
                                        if full_mask[i] != 0:
                                            last_zero_start = i + 1
                                            break
                                        if i == 0:
                                            last_zero_start = 0
                                
                                # 只有当0序列长度大于<documents_refine>标记长度时才删除
                                if (last_zero_start != -1 and last_zero_end != -1 \
                                    and last_zero_end - last_zero_start > relevant_info_length):
                                    
                                    last_zero_end = max(last_zero_end - relevant_info_length, 0)

                                    # 计算需要删除的token数量
                                    tokens_to_remove = last_zero_end - last_zero_start

                                    # 更新输入和掩码
                                    curr_inputs[idx] = init_inputs[idx] + full_tokens[:last_zero_start] + full_tokens[last_zero_end:]
                                    result_mask_list[idx] = full_mask[:last_zero_start] + full_mask[last_zero_end:]
                                    
                                    # 打印删除的内容
                                    removed_tokens = full_tokens[last_zero_start:last_zero_end]
                                    removed_text = self.tokenizer.decode(removed_tokens)

                            curr_inputs[idx] += output_ids
                            result_mask_list[idx] += [1] * len(output_ids)

                            # 最终验证
                            generated_length = len(curr_inputs[idx]) - len(init_inputs[idx])
                            assert len(result_mask_list[idx]) == generated_length, \
                                f"Final length mismatch: mask={len(result_mask_list[idx])}, generated={generated_length}"
                            new_active_indices.append(idx)
                    
                        else:
                            # 如果没有匹配的停止标记，继续生成
                            ## truncate from the first pad token
                            output_ids = output_ids[:first_pad_idx]
                            ## process the search
                            new_active_indices.append(idx)

                            ## update the current input
                            curr_inputs[idx] += output_ids
                            result_mask_list[idx] += [1] * len(output_ids)
                            # 不在这里增加检索次数计数，移到下方搜索结果处理部分
                            assert len(curr_inputs[idx]) - len(init_inputs[idx]) == len(result_mask_list[idx]), f"Length mismatch 1: {len(curr_inputs[idx])} vs {len(init_inputs[idx])}, {len(result_mask_list[idx])}"
                    
                    elif finish_reason == 'stop' and stop_reason == None:
                        # output eos, indicating finished; truncate from the first eos token
                        output_ids = output_ids[:first_eos_idx+1]
                        curr_inputs[idx] += output_ids
                        result_mask_list[idx] += [1] * len(output_ids)
                        assert len(curr_inputs[idx]) - len(init_inputs[idx]) == len(result_mask_list[idx]), f"Length mismatch 4: {len(curr_inputs[idx])} vs {len(init_inputs[idx])}, {len(result_mask_list[idx])}"
                    elif finish_reason == 'stop' and stop_reason == self.tokenizer.pad_token_id:
                        # for instruction model, there is a chance that the end is endoftext, not im_end, this case needs special handling
                        output_ids = output_ids[:first_pad_idx+1]
                        curr_inputs[idx] += output_ids
                        result_mask_list[idx] += [1] * len(output_ids)
                        assert len(curr_inputs[idx]) - len(init_inputs[idx]) == len(result_mask_list[idx]), f"Length mismatch 5: {len(curr_inputs[idx])} vs {len(init_inputs[idx])}, {len(result_mask_list[idx])}"
                    elif finish_reason == 'length':
                        # output is too long
                        curr_inputs[idx] += output_ids
                        result_mask_list[idx] += [1] * len(output_ids)
                        assert len(curr_inputs[idx]) - len(init_inputs[idx]) == len(result_mask_list[idx]), f"Length mismatch 6: {len(curr_inputs[idx])} vs {len(init_inputs[idx])}, {len(result_mask_list[idx])}"
                        
                # batch process the search requests
                if search_queries:
                    # search_results = self.batch_search(search_queries)
                    search_results = self.serper_search(search_queries)
                    
                    # 添加搜索结果到输入并保存搜索文档
                    for idx, result in zip(search_indices, search_results):
                        search_documents[idx].append(result)
                        if result == "":
                            print(f"search : retrieval_counts: {retrieval_counts[idx]}, curr_inputs[idx]: {self.tokenizer.decode(curr_inputs[idx])}")
                        
                        output_ids = self.tokenizer.encode(f" <documents>\n{result}\n</documents> <documents_refine>")
                        curr_inputs[idx] += output_ids
                        result_mask_list[idx] += [0] * len(output_ids)
                        assert len(curr_inputs[idx]) - len(init_inputs[idx]) == len(result_mask_list[idx]), f"Length search_doc mismatch 7: {len(curr_inputs[idx])} vs {len(init_inputs[idx])}, {len(result_mask_list[idx])}"

                # 更新最大输出长度限制
                length_checked_active_indices = []
                for idx in active_indices:
                    # 验证输入长度与掩码长度一致
                    assert len(curr_inputs[idx]) - len(init_inputs[idx]) == len(result_mask_list[idx]), f"8 curr_inputs: {len(curr_inputs[idx])}, init_inputs: {len(init_inputs[idx])}, result_mask_list: {len(result_mask_list[idx])}"
                    # 如果生成的响应长度超过了配置的最大响应长度，则截断
                    if len(curr_inputs[idx]) - len(init_inputs[idx]) >= self.config.response_length:
                        curr_inputs[idx] = init_inputs[idx] \
                            + curr_inputs[idx][len(init_inputs[idx]):len(init_inputs[idx])+self.config.response_length]
                        result_mask_list[idx] = result_mask_list[idx][:self.config.response_length]
                    # 如果未超过最大长度且需要继续生成，则更新 curr_max_tokens 和活跃索引列表
                    else:
                        curr_max_tokens[idx] = self.config.response_length - len(curr_inputs[idx]) + len(init_inputs[idx])
                        if idx in new_active_indices:
                            length_checked_active_indices.append(idx)
                    
                    # 验证retrievel_counts和search_documents的长度
                    assert retrieval_counts[idx] == len(search_documents[idx]), f"9 retrieval_counts: {retrieval_counts[idx]}, search_documents: {len(search_documents[idx])}, search_documents: {search_documents[idx]}"
                active_indices = length_checked_active_indices

            output_ids_list = []
            # collect the results
            # 收集所有输入的生成结果
            final_retrieval_counts = []
            final_search_documents = []
            for i, input_ids in enumerate(idx_list):
                for j in range(self.sampling_params.n):
                    idx = i * self.sampling_params.n + j
                    input_len = len(input_ids)
                    output_ids_list.append(curr_inputs[idx][input_len:])
                    final_retrieval_counts.append(retrieval_counts[idx])
                    final_search_documents.append(search_documents[idx])

        # 格式化和处理最终响应
        response_list = []
        result_mask_list_padded = []
        for output_ids, result_mask in zip(output_ids_list, result_mask_list):
            assert len(output_ids) == len(result_mask), f"output_ids: {len(output_ids)}, result_mask: {len(result_mask)}"
            
            # 处理response
            response = torch.tensor(output_ids, device=ori_input_ids.device, dtype=torch.int64)
            response = pad_sequence_to_length(response, self.config.response_length, self.pad_token_id)
            
            # 处理result_mask
            result_mask = torch.tensor(result_mask, device=ori_input_ids.device)
            result_mask = pad_sequence_to_length(result_mask, self.config.response_length, 0)
            
            response_list.append(response)
            result_mask_list_padded.append(result_mask)
            # search_docs_list.append(docs_tensor)

        response = torch.stack(response_list, dim=0)
        result_mask = torch.stack(result_mask_list_padded, dim=0)

        if self.config.n > 1 and do_sample:
            ori_input_ids = ori_input_ids.repeat_interleave(self.config.n, dim=0)
            attention_mask = attention_mask.repeat_interleave(self.config.n, dim=0)
            position_ids = position_ids.repeat_interleave(self.config.n, dim=0)
            batch_size = batch_size * self.config.n
            
        # 将原始输入和响应连接起来创建完整序列
        seq = torch.cat([ori_input_ids, response], dim=-1)

        response_length = response.size(1)
        delta_position_id = torch.arange(1, response_length + 1, device=position_ids.device)
        delta_position_id = delta_position_id.unsqueeze(0).repeat(batch_size, 1)

        # TODO(sgm): fix position_ids on right_pad
        # prompt: left pad + response: right pad
        # attention_mask: [0,0,0,0,1,1,1,1, | 1,1,1,0,0,0,0,0]
        # position_ids:   [0,0,0,0,0,1,2,3, | 4,5,6,7,8,9,10,11]
        # 计算响应的位置ID并与原始位置ID连接
        response_position_ids = position_ids[:, -1:] + delta_position_id
        position_ids = torch.cat([position_ids, response_position_ids], dim=-1)
        response_attention_mask = get_eos_mask(response_id=response, eos_token=eos_token_id, dtype=attention_mask.dtype)
        attention_mask = torch.cat((attention_mask, response_attention_mask), dim=-1)

        # result mask: result part is 0, other part is 1
        # 计算损失掩码（结合结果掩码和注意力掩码）
        loss_mask = result_mask * response_attention_mask

        # 修改部分：只保留每个样本的最后一次检索内容
        for i in range(len(final_retrieval_counts)):
            # 只保留最后一次检索的文档
            if final_retrieval_counts[i] > 0:
                # 如果有检索，只保留最后一个文档
                final_search_documents[i] = [final_search_documents[i][-1]]

        # # 将检索次数转换为张量
        retrieval_count_tensor = torch.tensor(final_retrieval_counts, device=ori_input_ids.device)

        MAX_DOC_LENGTH = self.config.response_length
        encoded_docs = []
        for docs in final_search_documents:
            if not docs:
                doc_tensor = torch.full((1,), self.pad_token_id, dtype=torch.long, device=ori_input_ids.device)
            else:
                combined_doc = docs[0]
                encoded = self.tokenizer.encode(combined_doc)[:MAX_DOC_LENGTH]  # 限长
                doc_tensor = torch.tensor(encoded, dtype=torch.long, device=ori_input_ids.device)
            encoded_docs.append(doc_tensor)

        search_docs_tensor_list = [pad_sequence_to_length(doc_tensor, MAX_DOC_LENGTH, self.pad_token_id) for doc_tensor in encoded_docs]

        search_docs_tensor = torch.stack(search_docs_tensor_list, dim=0)

        batch = TensorDict({
            'prompts': ori_input_ids,
            'responses': response,  # 完整的响应序列，全部的迭代的response
            'input_ids': seq,  # here input_ids become the whole sentences完整序列（提示+响应）
            'attention_mask': attention_mask,
            'loss_mask': loss_mask, # 损失掩码，用于训练时忽略搜索结果部分
            'position_ids': position_ids,
            'retrieval_counts': retrieval_count_tensor,  # 添加检索次数到返回结果中
            'search_documents': search_docs_tensor  # 添加转换后的搜索文档token IDs
        }, batch_size=batch_size)

        # free vllm cache engine
        if self.config.free_cache_engine:
            self.inference_engine.free_cache_engine()

        return DataProto(batch=batch)
