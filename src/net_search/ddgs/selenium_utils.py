"""
使用Selenium处理需要JavaScript的网站
需要安装: pip install selenium webdriver-manager
"""

import time
import random
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
import trafilatura
from bs4 import BeautifulSoup

def create_driver(headless=True, proxy=None):
    """创建Chrome WebDriver"""
    options = Options()
    
    if headless:
        options.add_argument('--headless')
    
    # 基本设置
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-gpu')
    options.add_argument('--window-size=1920,1080')
    
    # 反检测设置
    options.add_argument('--disable-blink-features=AutomationControlled')
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)
    
    # 设置User-Agent
    options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    # 代理设置
    if proxy:
        options.add_argument(f'--proxy-server={proxy}')
    
    try:
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
        
        # 执行反检测脚本
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        return driver
    except Exception as e:
        print(f"创建WebDriver失败: {e}")
        return None

def extract_text_with_selenium(url, wait_time=10, proxy=None):
    """使用Selenium提取需要JavaScript的网页内容"""
    driver = None
    try:
        # 设置代理
        proxy_url = "http://nbproxy.mlp.oppo.local:8888" if proxy is None else proxy
        
        driver = create_driver(headless=True, proxy=proxy_url)
        if not driver:
            return None
        
        print(f"使用Selenium访问: {url}")
        driver.get(url)
        
        # 等待页面加载
        time.sleep(random.uniform(2, 4))
        
        # 等待特定元素加载（可根据网站调整）
        try:
            WebDriverWait(driver, wait_time).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
        except TimeoutException:
            print("页面加载超时")
        
        # 滚动页面以触发懒加载
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight/2);")
        time.sleep(1)
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(1)
        driver.execute_script("window.scrollTo(0, 0);")
        time.sleep(1)
        
        # 获取页面源码
        html_content = driver.page_source
        
        # 使用trafilatura提取内容
        result = trafilatura.extract(html_content)
        
        if result:
            print(f"Selenium成功提取内容，长度: {len(result)} 字符")
            return result
        else:
            # 备用方案：使用BeautifulSoup
            print("trafilatura提取失败，使用BeautifulSoup...")
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 移除脚本和样式
            for element in soup(['script', 'style', 'nav', 'footer', 'header']):
                element.decompose()
            
            text = soup.get_text(separator='\n', strip=True)
            return text
            
    except WebDriverException as e:
        print(f"WebDriver错误: {e}")
        return None
    except Exception as e:
        print(f"未知错误: {e}")
        return None
    finally:
        if driver:
            driver.quit()

def extract_text_hybrid(url, use_selenium_fallback=True):
    """
    混合方案：先尝试requests，失败后使用selenium
    """
    from web_utils import extract_text
    
    print(f"首先尝试requests方式访问: {url}")
    result = extract_text(url, max_retries=3, delay_range=(1, 3))
    
    if result:
        return result
    
    if use_selenium_fallback:
        print(f"requests失败，尝试Selenium方式...")
        return extract_text_with_selenium(url)
    
    return None

if __name__ == '__main__':
    # 测试需要JavaScript的网站
    test_urls = [
        'https://www.bookbrowse.com/biographies/index.cfm/author_number/1687/Greg-Rucka',
        'https://en.wikipedia.org/wiki/Greg_Rucka',  # 也测试Wikipedia
    ]
    
    for url in test_urls:
        print('\n' + '='*60)
        print(f"测试URL: {url}")
        print('='*60)
        
        result = extract_text_hybrid(url)
        
        if result:
            print(f"✅ 成功提取内容 (前500字符):")
            print(result[:500] + "..." if len(result) > 500 else result)
        else:
            print("❌ 提取失败")
        
        # 添加延迟
        time.sleep(random.uniform(3, 6))
