import json
import requests
from selenium_utils import extract_text_concurrent
from web_utils import ContentCompress

class Query2UrlContent:

    def __init__(self, compress=False):
        self.compress = compress
        if compress:
            self.content_compress = ContentCompress()

    def serper_query(self, text: str, max_results: int=10):

        url = "https://google.serper.dev/search"

        payload = json.dumps({
            "q": f"{text}",
            "location": "United States",
            "num": max_results
        })
        headers = {
            'X-API-KEY': '3fa11d6a07bf67b82798ebefb2787ad9e84f21fd',
            'Content-Type': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload)
        response_json = json.loads(response.text)

        results = response_json.get('organic', [])

        re_results = []
        for result in results:
            re_results.append({
                'title': result['title'],
                'description': result['snippet'],
                'href': result['link'],
            })

        return re_results

    def url2content(self, urls: list[str]):
        contents = extract_text_concurrent(urls, max_workers=3, 
                                        use_selenium_fallback=True,
                                        progress_callback=lambda c, t, r: print(f"进度: {c}/{t}"),
                                        delay_between_batches=1.0)
        return contents

    def query2url_content(self, query: str, max_results: int=10):
        # 1. 获取摘要和 urls
        re_results = self.serper_query(query, max_results=max_results)
        urls = [re_result['href'] for re_result in re_results]

        # 2. 获取内容
        contents = self.url2content(urls)
        for result, content in zip(re_results, contents):
            if self.compress:
                content['content'] = self.content_compress.compress(content['content'])
            result['content'] = content['content']

        return re_results


if __name__ == '__main__':
    query2url_content = Query2UrlContent(compress=True)
    results = query2url_content.query2url_content("武松是哪个朝代的", max_results=5)
    print(results)
