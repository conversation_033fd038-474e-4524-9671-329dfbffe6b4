from duckduckgo_search import DDGS
from pprint import pprint
import json
from typing import Optional
import asyncio
import tempfile

from ddgs.constant import *

ddg = DDGS(proxy="http://nbproxy.mlp.oppo.local:8888")

def ddgs_text(query):
    results = ddg.text(query, max_results=5)
    for r in results:
        print(f"标题: {r['title']}")
        print(f"链接: {r['href']}")
        print(f"摘要: {r['body']}\n")
    return results

def ddgs_request(query):
    import requests
    url = "https://api.duckduckgo.com/"
    params = {
        "q": query,
        "format": "json",
        "no_redirect": 1,
        "no_html": 1,
        "skip_disambig": 1
    }
    headers = {
        "User-Agent": "my-app/0.0.1"  # 防止被识别为爬虫
    }
    response = requests.get(url, params=params, headers=headers)
    response.raise_for_status()
    data = response.json()

    if data.get("AbstractText"):
        print("摘要:", data["AbstractText"])
    else:
        print("无摘要，可能需要查看相关结果。")
        print(data)
    return data


if __name__ == "__main__":
    result = ddgs_text("Where was Greg Rucka born?")

    # 保存到文件
    with open("ddgs_result.json", "w") as f:
        json.dump(result, f, indent=4)
    pprint(result)
