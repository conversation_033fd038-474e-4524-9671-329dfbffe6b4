def run_item(self, item):
    """处理单个问题项目，执行生成、检索和结果提取流程
    
    Args:
        item: 问题项，包含question字段和更新输出的方法
    """
    # 准备输入查询
    if self.apply_chat:
        query = self.tokenizer.apply_chat_template([
            {'role': 'system', 'content': self.prompt_template},
            {'role': 'user', 'content': item.question}
        ], tokenize=False, add_generation_prompt=True)
    else:
        query = self.prompt_template.format(prompt=item.question)
    
    init_query = query
    
    # 将查询编码为token IDs
    input_ids = self.tokenizer.encode(query, return_tensors="pt").to(self.device)
    attention_mask = torch.ones_like(input_ids)
    position_ids = torch.cumsum(attention_mask, dim=1) - 1
    
    # 创建用于模型输入的DataProto
    prompts = DataProto(
        batch={
            'input_ids': input_ids,
            'attention_mask': attention_mask,
            'position_ids': position_ids
        },
        meta_info={
            'eos_token_id': self.tokenizer.eos_token_id,
            'do_sample': self.do_sample,
            'max_time': self.max_retrieval_times  # 最大检索次数
        }
    )
    
    # 使用改进的generate_sequences方法生成响应
    outputs = self.generator.generate_sequences(prompts)
    
    # 从输出中提取生成的响应
    response_ids = outputs.batch['responses'][0]  # 取第一个批次的响应
    
    # 解码响应
    mask = response_ids != self.tokenizer.pad_token_id
    if mask.sum() > 0:
        response_text = self.tokenizer.decode(response_ids[mask])
    else:
        response_text = ""
    
    # 获取检索次数
    retrieval_count = outputs.batch.get('retrieval_counts', [0])[0].item()
    
    # 获取搜索结果文档（如果有）
    search_docs = None
    if 'search_documents' in outputs.batch:
        search_docs_ids = outputs.batch['search_documents'][0]
        mask = search_docs_ids != self.tokenizer.pad_token_id
        if mask.sum() > 0:
            search_docs = self.tokenizer.decode(search_docs_ids[mask])
            # 使用分隔符分割为不同的文档
            if " [DOC_SEP] " in search_docs:
                search_docs = search_docs.split(" [DOC_SEP] ")
            else:
                search_docs = [search_docs]
    
    # 更新项目输出
    item.update_output("final_response", response_text)
    item.update_output("retrieval_count", retrieval_count)
    if search_docs:
        item.update_output("search_documents", search_docs)
    
    # 提取答案
    try:
        answer_part = extract_answer(response_text)
        if answer_part is not None:
            try:
                answer = remove_boxed(last_boxed_only_string(answer_part))
            except Exception:
                answer = ''
        else:
            answer = ""
    except:
        answer = ""
    
    item.update_output("pred", answer) 