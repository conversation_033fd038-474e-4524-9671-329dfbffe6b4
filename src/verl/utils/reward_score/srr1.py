import re
import sys
import string
from typing import Union, List
from collections import Counter

def validate_format(text: str) -> tuple[bool, str]:
    # Check for basic tag issues
    if text.count('<') != text.count('>'):
        return False, "< > not paired"
    if text.count('<query>') == 0:
        return False, "<query> not found"
    if text.count('<answer>') != 1 or text.count('</answer>') != 1:
        return False, "<answer> or </answer> not found"        
    # 添加对<documents>标签的检查
    if text.count('<documents>') > 0 or text.count('</documents>') > 0:
        return False, "should not contain <documents></documents> tags"
    
    # Check the order of query/documents_refine
    current_pos = 0
    while True:
        query_pos = text.find('<query>', current_pos)
        if query_pos == -1:
            break
            
        documents_refine_pos = text.find('<documents_refine>', query_pos)
        query_end_pos = text.find('</query>', query_pos)
        documents_refine_end_pos = text.find('</documents_refine>', documents_refine_pos)
        
        if -1 in (documents_refine_pos, query_end_pos, documents_refine_end_pos):
            return False, "query/documents_refine tags are incomplete"
            
        if not (query_pos < query_end_pos < documents_refine_pos < documents_refine_end_pos):
            return False, "query/documents_refine tags are nested in the wrong order"
            
        current_pos = documents_refine_end_pos
    
    # Check if \boxed{} is in the answer
    answer_start = text.find('<answer>')
    answer_end = text.find('</answer>')
    if answer_start > answer_end:
        return False, "<answer> must be before </answer>"
    answer_content = text[answer_start + len('<answer>'):answer_end]
    if '\\boxed{' not in answer_content or '}' not in answer_content:
        return False, "answer is missing \\boxed{} format"
    
    return True, "format is correct"


def extract_answer(text: str):
    text = text.strip()

    pattern = r"<answer>(.*?)</answer>"
    match = re.search(pattern, text, re.DOTALL)
    if not match:
        return None
    
    return match.group(1)

def remove_boxed(s):
    if "\\boxed " in s:
        left = "\\boxed "
        assert s[:len(left)] == left
        return s[len(left):]

    left = "\\boxed{"

    assert s[:len(left)] == left
    assert s[-1] == "}"

    return s[len(left):-1]

def last_boxed_only_string(string):
    idx = string.rfind("\\boxed")
    if "\\boxed " in string:
        return "\\boxed " + string.split("\\boxed ")[-1].split("$")[0]
    if idx < 0:
        idx = string.rfind("\\fbox")
        if idx < 0:
            return None

    i = idx
    right_brace_idx = None
    num_left_braces_open = 0
    while i < len(string):
        if string[i] == "{":
            num_left_braces_open += 1
        if string[i] == "}":
            num_left_braces_open -= 1
            if num_left_braces_open == 0:
                right_brace_idx = i
                break
        i += 1

    if right_brace_idx is None:
        retval = None
    else:
        retval = string[idx:right_brace_idx + 1]

    return retval

def normalize_answer(s):
    def remove_articles(text):
        return re.sub(r"\b(a|an|the)\b", " ", text)

    def white_space_fix(text):
        return " ".join(text.split())

    def remove_punc(text):
        exclude = set(string.punctuation)
        return "".join(ch for ch in text if ch not in exclude)

    def lower(text):
        return text.lower()

    return white_space_fix(remove_articles(remove_punc(lower(s))))

def normalize_doc(s):
    def white_space_fix(text):
        return " ".join(text.split())
    def remove_punc(text):
        exclude = set(string.punctuation)
        return "".join(ch for ch in text if ch not in exclude)
    def lower(text):
        return text.lower()
    return white_space_fix(remove_punc(lower(s)))


def get_f1_score(prediction: str, ground_truths: Union[str, List[str]]):
    if isinstance(ground_truths, str):
        ground_truths = [ground_truths]
    
    final_metric = {"f1": 0, "precision": 0, "recall": 0}

    for ground_truth in ground_truths:
        normalized_prediction = normalize_answer(prediction)
        normalized_ground_truth = normalize_answer(ground_truth)

        if normalized_prediction in ["yes", "no", "noanswer"] and normalized_prediction != normalized_ground_truth:
            continue
        
        if normalized_ground_truth in ["yes", "no", "noanswer"] and normalized_prediction != normalized_ground_truth:
            continue

        prediction_tokens = normalized_prediction.split()
        ground_truth_tokens = normalized_ground_truth.split()
        common = Counter(prediction_tokens) & Counter(ground_truth_tokens)
        num_same = sum(common.values())
        if num_same == 0:
            continue
        
        precision = 1.0 * num_same / len(prediction_tokens)
        recall = 1.0 * num_same / len(ground_truth_tokens)
        f1 = (2 * precision * recall) / (precision + recall)
        
        final_metric["precision"] = max(precision, final_metric["precision"])
        final_metric["recall"] = max(recall, final_metric["recall"])
        final_metric["f1"] = max(f1, final_metric["f1"])
    
    return final_metric['f1']

def check_result_content(text: str, ground_truths: Union[str, List[str]]) -> bool:

    """
    Extract content from the last <documents></documents> pair and check if any item from ground_truth exists in it.
    
    Args:
        text: The text to query in
        ground_truth: List of reference texts to check for
        
    Returns:
        tuple[bool, str]: (True/False indicating if any ground_truth item is in result, explanation message)
    """
    if isinstance(ground_truths, str):
        ground_truths = [ground_truths]

    # Find the last occurrence of <documents> and </documents>
    last_result_start = text.rfind("<documents>")
    last_result_end = text.rfind("</documents>")
    
    if last_result_start == -1 or last_result_end == -1:
        return False
    
    if last_result_start >= last_result_end:
        return False
    
    # Extract the content between the last <documents> and </documents> tags
    result_content = text[last_result_start + len("<documents>"):last_result_end].strip()
    
    # Normalize the result content
    normalized_result = normalize_answer(result_content)
    
    # Check if any ground_truth item exists within the result
    found_items = []
    
    for item in ground_truths:
        normalized_item = normalize_answer(item)
        if normalized_item in normalized_result:
            found_items.append(item)
    
    if found_items:
        # print("in result")
        # print(f"last_content: {text[last_result_start + len('<documents>'):last_result_end]}")
        # print(f"ground_truth: {ground_truth}")
        return True
    else:
        # print("out of result")
        # print(f"last_doc: {result_content}")
        # print(f"ground_truth: {ground_truth}")
        return False


def format_score_compute(response, tokenizer):
    valid_template, reason = validate_format(response)
    if not valid_template:
        return 0, f'bad format: {reason}'

    if response.endswith(tokenizer.eos_token):
        response = response[:-len(tokenizer.eos_token)]
    else:
        return 0, f'over length or over retrieve'

    answer_part = extract_answer(response)
    if answer_part is not None:
        try:
            answer = remove_boxed(last_boxed_only_string(answer_part))
            return 1, 'success'  # Add this line to return success case
        except Exception as e:
            return 0, f'find box error: {e}'
    else:
        return 0, f'cannot extract answer'

def extract_documents_refine_by_index(text: str, index: int) -> str:
    """Extract the content of the nth <documents_refine> tag in the text"""
    pattern = r"<documents_refine>(.*?)</documents_refine>"
    matches = re.findall(pattern, text, re.DOTALL)
    if index < len(matches):
        return matches[index].strip()
    return ""

def check_in_both_sources(text: str, ground_truths: Union[str, List[str]], query_doc: str, index: int) -> tuple[float, str]:
    """检查ground_truth是否在文档和refine中，返回分数和原因"""
    if isinstance(ground_truths, str):
        ground_truths = [ground_truths]
    
    normalized_doc = normalize_doc(query_doc)
    
    # 检查是否在文档中
    in_doc = False
    for ground_truth in ground_truths:
        normalized_truth = normalize_doc(ground_truth)
        if normalized_truth in normalized_doc:
            in_doc = True
            break
    
    # 检查在refine中的情况
    relevant_content = extract_documents_refine_by_index(text, index)
    if not in_doc:
        return 0.0, f"not in last document, query_doc: {query_doc}, last_refine: {relevant_content}"
    if not relevant_content:
        return 0.3, f"in last document but do not have last_refine, query_doc: {query_doc}, last_refine: {relevant_content}"
    
    normalized_relevant = normalize_doc(relevant_content)
    
    # 检查是否同时在refine中
    in_refine = False
    for ground_truth in ground_truths:
        normalized_truth = normalize_doc(ground_truth)
        if normalized_truth in normalized_relevant:
            in_refine = True
            break
    
    if in_refine:
        return 0.6, f"in both document and last_refine, query_doc: {query_doc}, last_refine: {relevant_content}"
    else:
        return 0.3, f"in document but not in last_refine, query_doc: {query_doc}, last_refine: {relevant_content}"

def len_score(retrival_times, max_retrival_times=5):
    '''
    奖励函数，用于衡量模型在自身生成序列过程中检索次数的惩罚
    具体来说检索次数小于等于三次则不惩罚，大于3次时，每多一次检索，惩罚增加1
    Args:
        retrival_times: 检索次数列表
    Returns:
        score: 奖励分数 (范围0.1-0.2)
    '''
    start = 3 # 检索次数小于等于 start 时，不惩罚
    penalty = 0.1 # 惩罚系数调整为0.1，使最大值为0.2

    if retrival_times <= start:
        return 0.2
    else:
        return max(0.1, 0.2 - penalty * (retrival_times - start) / (max_retrival_times - start))

def compute_score(tokenizer, solution_str, ground_truth, retrieval_count, query_doc) -> float:
    # handling both the base model and the instruction-tuned model
    if "<|im_start|>assistant\n" in solution_str:
        solution_str_split = solution_str.split("<|im_start|>assistant\n")
    else:
        solution_str_split = solution_str.split("Assistant:")
    
    # 1. 验证回复格式
    response = solution_str_split[1]
    valid_template, reason = validate_format(response)

    if not valid_template:
        return 0, f'bad format: {reason}'

    # 获取有效回复结果（去除填充符）
    if response.endswith(tokenizer.eos_token):
        response = response[:-len(tokenizer.eos_token)]
    else:
        return 0, f'over length'
    
    # 2. 验证 response 字符串是否以 </query> 结尾的
    if response.endswith("</query>"):
        return 0, f'over retrieve'
    
    # 3. 验证 answer 格式是否正确
    answer_part = extract_answer(response)
    if answer_part is not None:
        try:
            answer = remove_boxed(last_boxed_only_string(answer_part))
        except Exception as e:
            return 0, f'find box error: {e}'
    else:
        return 0, f'cannot extract answer'

    # 4. 验证 ground_truth 是否在最后一个 doc，最后一个 refine 中
    gt_in_soc, reason = check_in_both_sources(response, ground_truth, query_doc, retrieval_count-1)
    if gt_in_soc == 0.0:
        return 0.1, f"{reason}, correct format, answer: {answer}, ground_truth: {ground_truth}, retrieval_count: {retrieval_count}"
    elif gt_in_soc == 0.3:
        return gt_in_soc + len_score(retrieval_count), f"{reason}, correct format, answer: {answer}, ground_truth: {ground_truth}, retrieval_count: {retrieval_count}"
    else:
        f1 = get_f1_score(answer, ground_truth)
        if f1 == 0:
            return gt_in_soc + len_score(retrieval_count), f"{reason}, correct format, in last doc, but not correct answer, answer: {answer}, ground_truth: {ground_truth}, retrieval_count: {retrieval_count}"
        else:
            return gt_in_soc +  f1 + len_score(retrieval_count), f"{reason}, correct format, in last doc, and f1 score: {f1}, answer: {answer}, ground_truth: {ground_truth}, retrieval_count: {retrieval_count}"
    # else:
    #     return 0.1, f'correct format, but out of last doc'