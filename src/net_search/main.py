from utils import ddgs_text , ddgs_request
from ddgs.full_utils import *
from tqdm import tqdm
import time
import asyncio
import aiohttp

# 异步压测函数
async def async_test_speed():
    query = "Python programming"
    total_requests = 11
    requests_per_second = 10

    async def async_ddgs_request(session, query):
        url = "https://api.duckduckgo.com/"
        params = {
            "q": query,
            "format": "json",
            "no_redirect": 1,
            "no_html": 1,
            "skip_disambig": 1
        }
        headers = {
            "User-Agent": "my-app/0.0.1"
        }
        headers = {
            "User-Agent": "my-app/0.0.1",
            "Accept": "application/json"  # 明确要求JSON响应
        }
        
        async with session.get(url, params=params, headers=headers) as response:
            response.raise_for_status()
            
            # 尝试解析JSON，失败则返回原始文本
            try:
                if 'application/json' in response.headers.get('Content-Type', ''):
                    data = await response.json()
                    return data.get("AbstractText")
                else:
                    text = await response.text()
                    return text  # 返回原始响应文本
            except Exception as e:
                print(f"Error processing response: {e}")
                return None
    
    # 配置连接器
    connector = aiohttp.TCPConnector(
        limit=20,  # 限制并发连接数
        force_close=True,
        enable_cleanup_closed=True,
        ssl=False  # 禁用SSL验证（如果需要）
    )
    
    # 配置超时
    timeout = aiohttp.ClientTimeout(total=30)  # 总超时30秒
    
    # 使用代理和超时配置创建会话
    async with aiohttp.ClientSession(
        connector=connector,
        timeout=timeout,
        trust_env=True  # 自动从环境变量读取代理配置
    ) as session:
        # 使用tqdm显示总进度
        with tqdm(total=total_requests, desc="总请求进度") as pbar:
            for batch in range(0, total_requests, requests_per_second):
                batch_tasks = []
                batch_start = time.time()
                
                # 创建当前批次的10个任务
                for _ in range(requests_per_second):
                    task = asyncio.create_task(async_ddgs_request(session, query))
                    batch_tasks.append(task)
                    pbar.update(1)  # 更新进度条
                
                # 执行当前批次的请求
                results = await asyncio.gather(*batch_tasks)
                
                # 计算并等待剩余时间，确保每秒精确10次
                batch_time = time.time() - batch_start
                if batch_time < 1.0:
                    await asyncio.sleep(1.0 - batch_time)
                
                print(results)

# 压测，每2秒一次
def test_speed():
    query = "Python programming"
    for i in tqdm(range(20)):
        time_start = time.time()
        results = ddgs_text(query)
        print(results)
        time_spend = time.time() - time_start
        if time_spend < 3:
            time.sleep(2 - time_spend)
        # time.sleep(1)

if __name__ == "__main__":
    serper = SerperQueryRun(max_results=5)
    # result = asyncio.run(serper("武松是哪个朝代的", parse_url=True))
    # result = search_text_brief_result("武松是哪个朝代的", max_result_num=5, screenshot_dir='/home/<USER>/code/personal/S9058924/SRR1/src/net_search/results')
    result = get_fullpage_content(r"https://zh.wikipedia.org/wiki/%E6%AD%A6%E6%9D%BE")
    json_result = json.dumps(result, ensure_ascii=False)
    print(result)
    with open('/home/<USER>/code/personal/S9058924/SRR1/src/net_search/results/serper.json', 'w') as f:
        json.dump(json_result, f, ensure_ascii=False, indent=4)

    # asyncio.run(async_test_speed())