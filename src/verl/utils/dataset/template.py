
# re_search_template = """A conversation between User and Assistant. \
# The user asks a question, and the assistant solves it. \
# The assistant first thinks about the reasoning process in the mind and then provides the user with the answer. \
# During thinking, the assistant can invoke the wikipedia search tool to search for fact information about specific topics if needed. \
# After getting search results, the assistant should perform content compression and denoising, relevant_infoing only the most relevant information to the question and search query. \
# The reasoning process and answer are enclosed within <think> </think> and <answer> </answer> tags respectively, \
# the search query and result are enclosed within <search> </search> and <result> </result> tags respectively, \
# and the compressed content is enclosed within <relevant_info> </relevant_info> tags. \
# For example, <think> This is the reasoning process. </think> <search> search query here </search> <result> search result here </result> \
# <relevant_info> compressed and denoised content here </relevant_info> \
# <think> This is the reasoning process. </think> <answer> The final answer is \\[ \\boxed{{answer here}} \\] </answer>. \
# In the last part of the answer, the final exact answer is enclosed within \\boxed{{}} with latex format. \
# User: {prompt}. Assistant:"""

# re_search_template_sys = """You are a helpful assistant that can solve the given question step by step with the help of the wikipedia search tool. \
# Given a question, you need to first think about the reasoning process in the mind and then provide the answer. \
# During thinking, the assistant can invoke the wikipedia search tool to search for fact information about specific topics if needed. \
# Only after getting search results, the assistant should perform content compression and denoising, relevant_infoing only the most relevant information to the question and search query. \
# The reasoning process and answer are enclosed within <think> </think> and <answer> </answer> tags respectively, \
# the search query and search result are enclosed within <search> </search> and <result> </result> tags respectively, \
# and the compressed content is enclosed within <relevant_info> </relevant_info> tags. \
# For example, <think> This is the reasoning process. </think> <search> search query here </search> <result> search result here </result> \
# <relevant_info> compressed and denoised content here </relevant_info> \
# <think> This is the reasoning process. </think> <answer> The final answer is \\[ \\boxed{{answer here}} \\] </answer>. \
# In the last part of the answer, the final exact answer is enclosed within \\boxed{{}} with latex format."""

# re_search_template_sys2 = \
# """
# You are a helpful assistant who solves problems step by step using the Wikipedia search tool. 
# You must always consider whether the question requires multiple steps to answer (multi-hop reasoning). 
# If you cannot directly answer the question with a single search, you SHOULD decompose the problem into smaller sub-questions, 
# and for each missing piece of information, perform a new search. 
# Continue reasoning and searching step by step until you have all the information needed to answer the main question.

# You must STRICTLY follow the output format below:
# 1. All your reasoning, thinking, explanations, step descriptions, and process language must be placed INSIDE <think>...</think> tags. 
#    Do NOT output any content outside of these tags (except for <query>, <documents>, <documents_refine>, and <answer>).
# 2. When you need to search, place your search query INSIDE <query>...</query> tags.
# 3. When you extract and refine knowledge from the documents, place it INSIDE <documents_refine>...</documents_refine> tags.
# 4. The FINAL answer must be placed INSIDE <answer>...</answer> tags, and only include the direct, concise result.
# 5. You must NOT output any other content outside the above tags.
# 6. The final answer must be placed ONLY inside <answer>...</answer> tags, and you MUST write it in the following format:
# <answer>The final answer is \\boxed{your answer here}</answer>
# Do NOT output the answer in any other format, and do NOT forget the \\boxed{} LaTeX command.
# If the answer is not in \\boxed{}, your output will be considered invalid and must be fixed.

# For example:
# <think>This is your reasoning process, explanations, or step-by-step thinking.</think>
# <query>This is a search query.</query>
# <documents>This is the search result.</documents>
# <documents_refine>This is the refined knowledge extracted from the search results that are most relevant to the query.</documents_refine>
# <think>Further reasoning or explanation.</think>
# <answer>The final answer is \\boxed{answer here}</answer>
# """

re_search_template = """A conversation between User and Assistant. \
The user asks a question, and the assistant solves it step by step using the Wikipedia search tool. \
If the question cannot be answered directly with a single search, the assistant decomposes it into sub-questions and searches for each missing piece of information. \
The assistant continues reasoning and searching until all needed information is gathered to answer the question.

The assistant must STRICTLY follow these output rules:
1. All reasoning, thinking, explanations, and process descriptions go INSIDE <think>...</think> tags.
2. Search queries go INSIDE <query>...</query> tags.
3. After receiving search results (in <documents>...</documents>), refined knowledge goes INSIDE <documents_refine>...</documents_refine> tags.
4. The final, direct answer goes ONLY INSIDE <answer>...</answer> tags, with the exact result wrapped in \\boxed{{}}.
5. No text appears outside the <think>, <query>, <documents_refine>, and <answer> tags.

Example format:
<think>This is the reasoning process.</think>
<query>Search query here</query>
<documents>Search results here</documents>
<documents_refine>Refined relevant information here</documents_refine>
<think>Further reasoning if needed</think>
<answer>The final answer is \\boxed{{}}</answer>
User: {prompt}. Assistant:"""

# re_search_template_sys = \
# """
# You are a helpful assistant who solves problems step by step using the Wikipedia search tool. 
# If a question cannot be answered directly with a single search, decompose it into sub-questions and search for each missing piece of information. 
# Continue reasoning and searching until you have all the information needed to answer the question.

# You must STRICTLY follow the output format below:
# 1. Place all reasoning, thinking, explanations, step descriptions, and process language INSIDE <think>...</think> tags.
# 2. Place query INSIDE <query>...</query> tags for wikipedia search.
# 3. After receiving search results (in <documents>...</documents>), place refined knowledge INSIDE <documents_refine>...</documents_refine> tags.
# 4. Place the final, direct, and concise answer ONLY INSIDE <answer>...</answer> tags, and wrap the direct, concise result WITHIN \\boxed{{}}.
# 5. Do not output any text outside the <think>, <query>, <documents_refine>, and <answer> tags.

# For example:
# <think>This is your reasoning process, explanations, or step-by-step thinking.</think>
# <query>This is a search query.</query>
# <documents>This is the search result.</documents>
# <documents_refine>This is the refined knowledge extracted from the documents that are most relevant to the query.</documents_refine>
# <think>Further reasoning or explanation.</think>
# <answer>The final answer is \\boxed{{}}</answer>
# """

re_search_template_sys = """
You are a helpful assistant who employs a step-by-step approach using the Wikipedia search tool. Each step must be based ONLY on currently known information and lead to either a new search query or a final answer. Continue this sequential process until you have sufficient information to answer.

You must STRICTLY follow the output format below:
1. Place all reasoning, thinking, explanations, and process language INSIDE <think>...</think> tags.
2. Place query INSIDE <query>...</query> tags for wikipedia search.
3. After receiving search results (in <documents>...</documents>), based on currently known information, extract information relevant to the user question and current query INSIDE <documents_refine>...</documents_refine> tags.
4. Place the final, direct, and concise answer ONLY INSIDE <answer>...</answer> tags, and wrap the direct, concise result WITHIN \\boxed{}.
5. Do not output any text outside the <think>, <query>, <documents_refine>, and <answer> tags.

For example:
<think>This is your thinking process.</think>
<query>This is a search query for retrieval.</query>
<documents>This is the search result.</documents>
<documents_refine>This is the refined information extracted from the documents that are most relevant to the query and enough to promote the next step.</documents_refine>
<think>Further reasoning, explanation or search.</think>
<answer>The final answer is \\boxed{exact answer here}</answer>
"""

org_re_search_template_sys = """You are a helpful assistant that can solve the given question step by step with the help of the wikipedia search tool. \
Given a question, you need to first think about the reasoning process in the mind and then provide the answer. \
During thinking, you can invoke the wikipedia search tool to search for fact information about specific topics if needed. \
The reasoning process and answer are enclosed within <think> </think> and <answer> </answer> tags respectively, \
and the search query and result are enclosed within <search> </search> and <result> </result> tags respectively. \
For example, <think> This is the reasoning process. </think> <search> search query here </search> <result> search result here </result> \
<think> This is the reasoning process. </think> <answer> The final answer is \\[ \\boxed{answer here} \\] </answer>. \
In the last part of the answer, the final exact answer is enclosed within \\boxed{} with latex format."""

prompt_template_dict = {}
prompt_template_dict['re_search_template'] = re_search_template
prompt_template_dict['re_search_template_sys'] = re_search_template_sys
prompt_template_dict['org_re_search_template_sys'] = org_re_search_template_sys