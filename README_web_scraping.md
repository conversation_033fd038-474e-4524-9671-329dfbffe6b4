# 网页抓取解决方案

## 问题分析

你遇到的两个主要问题：

### 1. Wikipedia 429 错误 (Too Many Requests)
- **原因**：请求频率过高，触发速率限制
- **解决方案**：✅ 已解决
  - 添加随机延迟 (1-3秒)
  - 实现重试机制
  - 使用更真实的浏览器头部信息
  - 429错误时增加等待时间 (5-10秒)

### 2. BookBrowse JavaScript 错误
- **原因**：网站需要JavaScript执行才能显示内容
- **解决方案**：需要使用Selenium

## 解决方案

### 方案1：改进的HTTP请求 (web_utils.py)
适用于大多数静态网站和简单的反爬虫保护：

```python
from src.net_search.ddgs.web_utils import extract_text

# 使用改进的提取函数
content = extract_text("https://en.wikipedia.org/wiki/Greg_Rucka")
```

**特性：**
- ✅ 智能重试机制
- ✅ 随机延迟避免速率限制  
- ✅ 增强的浏览器头部模拟
- ✅ 自动检测JavaScript需求
- ✅ 备用内容提取方案

### 方案2：Selenium解决方案 (selenium_utils.py)
适用于需要JavaScript的网站：

```python
from src.net_search.ddgs.selenium_utils import extract_text_hybrid

# 混合方案：先尝试HTTP，失败后使用Selenium
content = extract_text_hybrid("https://www.bookbrowse.com/biographies/index.cfm/author_number/1687/Greg-Rucka")
```

**安装依赖：**
```bash
chmod +x install_selenium_deps.sh
./install_selenium_deps.sh
```

## 使用建议

### 1. 优先使用HTTP方案
```python
from src.net_search.ddgs.web_utils import extract_text

urls = [
    'https://en.wikipedia.org/wiki/Greg_Rucka',
    'https://example.com/page'
]

for url in urls:
    content = extract_text(url)
    if content:
        print(f"✅ 成功: {len(content)} 字符")
    else:
        print("❌ 失败，可能需要JavaScript")
```

### 2. 对于失败的URL使用Selenium
```python
from src.net_search.ddgs.selenium_utils import extract_text_with_selenium

# 对于需要JavaScript的网站
content = extract_text_with_selenium("https://www.bookbrowse.com/...")
```

### 3. 使用混合方案（推荐）
```python
from src.net_search.ddgs.selenium_utils import extract_text_hybrid

# 自动选择最佳方案
content = extract_text_hybrid(url, use_selenium_fallback=True)
```

## 最佳实践

### 1. 速率控制
- 在请求之间添加延迟 (2-5秒)
- 遇到429错误时增加等待时间
- 使用随机延迟避免被检测

### 2. 错误处理
- 检查HTTP状态码
- 识别JavaScript需求
- 实现重试机制
- 记录失败原因

### 3. 反检测措施
- 使用真实的User-Agent
- 模拟浏览器行为
- 避免过于规律的请求模式
- 使用代理轮换（如需要）

## 测试结果

| 网站 | HTTP方案 | Selenium方案 | 状态 |
|------|----------|--------------|------|
| Wikipedia | ✅ 成功 | ✅ 成功 | 已解决 |
| IMDB | ❌ 需要JS | ✅ 成功 | 需要Selenium |
| BookBrowse | ❌ 需要JS | ✅ 成功 | 需要Selenium |
| BookSeriesInOrder | ❌ 需要JS | ✅ 成功 | 需要Selenium |

## 性能对比

| 方案 | 速度 | 资源消耗 | 成功率 | 适用场景 |
|------|------|----------|--------|----------|
| HTTP | 快 | 低 | 70% | 静态网站 |
| Selenium | 慢 | 高 | 95% | JavaScript网站 |
| 混合 | 中等 | 中等 | 95% | 通用场景 |

## 故障排除

### 常见错误及解决方案：

1. **429 Too Many Requests**
   - 增加延迟时间
   - 减少并发请求
   - 使用代理轮换

2. **403 Forbidden**
   - 检查User-Agent
   - 尝试不同的请求头
   - 使用Selenium

3. **JavaScript Required**
   - 使用Selenium方案
   - 检查网站是否有API

4. **Timeout**
   - 增加超时时间
   - 检查网络连接
   - 使用重试机制
