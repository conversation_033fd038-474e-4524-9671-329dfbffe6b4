# 🚀 并发网页提取 - 快速开始

## 一分钟上手

### 基础用法

```python
from src.net_search.ddgs.selenium_utils import extract_text_concurrent

# 要提取的URL列表
urls = [
    'https://en.wikipedia.org/wiki/Python',
    'https://en.wikipedia.org/wiki/JavaScript',
    'https://www.example.com'
]

# 并发提取
results = extract_text_concurrent(urls, max_workers=3)

# 处理结果
for result in results:
    if result['success']:
        print(f"✅ {result['url']}: {len(result['content'])} 字符")
    else:
        print(f"❌ {result['url']}: {result['error']}")
```

### 带进度显示

```python
def show_progress(completed, total, result):
    percentage = (completed / total) * 100
    status = "✅" if result['success'] else "❌"
    print(f"进度: {percentage:5.1f}% {status} {result['url']}")

results = extract_text_concurrent(
    urls=urls,
    max_workers=3,
    progress_callback=show_progress
)
```

## 核心参数

| 参数 | 默认值 | 说明 | 推荐设置 |
|------|--------|------|----------|
| `max_workers` | 3 | 并发线程数 | 2-5 (根据网站类型) |
| `use_selenium_fallback` | True | 是否使用Selenium备用 | True (推荐) |
| `delay_between_batches` | 1.0 | 批次间延迟(秒) | 0.5-2.0 |

## 实际应用场景

### 场景1: 批量抓取Wikipedia

```python
# Wikipedia页面列表
wiki_urls = [
    'https://en.wikipedia.org/wiki/Python_(programming_language)',
    'https://en.wikipedia.org/wiki/Machine_learning',
    'https://en.wikipedia.org/wiki/Artificial_intelligence',
]

results = extract_text_concurrent(
    urls=wiki_urls,
    max_workers=3,  # Wikipedia比较宽松
    delay_between_batches=0.5
)

# 保存结果
for result in results:
    if result['success']:
        filename = result['url'].split('/')[-1] + '.txt'
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(result['content'])
        print(f"保存: {filename}")
```

### 场景2: 混合网站类型

```python
# 包含需要JavaScript的网站
mixed_urls = [
    'https://en.wikipedia.org/wiki/Greg_Rucka',  # 静态
    'https://m.imdb.com/name/nm1421638/',        # 需要JS
    'https://www.bookbrowse.com/...',            # 需要JS
]

def detailed_progress(completed, total, result):
    method = f"[{result['method']}]" if result['method'] else "[失败]"
    print(f"{completed}/{total}: {method} {result['url']}")

results = extract_text_concurrent(
    urls=mixed_urls,
    max_workers=2,  # 保守设置
    use_selenium_fallback=True,
    progress_callback=detailed_progress,
    delay_between_batches=2.0  # 更长延迟
)
```

### 场景3: 大批量处理

```python
# 大量URL
large_url_list = [f"https://en.wikipedia.org/wiki/Topic_{i}" for i in range(100)]

# 分批处理
batch_size = 20
all_results = []

for i in range(0, len(large_url_list), batch_size):
    batch_urls = large_url_list[i:i+batch_size]
    print(f"处理批次 {i//batch_size + 1}/{(len(large_url_list)-1)//batch_size + 1}")
    
    batch_results = extract_text_concurrent(
        urls=batch_urls,
        max_workers=5,
        use_selenium_fallback=False,  # 只用requests，更快
        delay_between_batches=0.3
    )
    
    all_results.extend(batch_results)
    
    # 批次间休息
    import time
    time.sleep(5)

print(f"总共处理: {len(all_results)} 个URL")
```

## 性能优化建议

### 根据网站类型调整参数

```python
# 开放网站 (如Wikipedia)
results = extract_text_concurrent(
    urls=wiki_urls,
    max_workers=5,
    delay_between_batches=0.5
)

# 商业网站 (更保守)
results = extract_text_concurrent(
    urls=commercial_urls,
    max_workers=2,
    delay_between_batches=2.0
)

# 严格反爬网站 (最保守)
results = extract_text_concurrent(
    urls=strict_urls,
    max_workers=1,
    delay_between_batches=5.0
)
```

### 错误处理和重试

```python
def extract_with_retry(urls, max_retries=2):
    """带重试的提取函数"""
    all_results = []
    remaining_urls = urls.copy()
    
    for attempt in range(max_retries + 1):
        if not remaining_urls:
            break
            
        print(f"尝试 {attempt + 1}/{max_retries + 1}, 剩余 {len(remaining_urls)} 个URL")
        
        results = extract_text_concurrent(
            urls=remaining_urls,
            max_workers=2 if attempt > 0 else 3,  # 重试时更保守
            delay_between_batches=2.0 if attempt > 0 else 1.0
        )
        
        # 分离成功和失败的结果
        successful = [r for r in results if r['success']]
        failed = [r for r in results if not r['success']]
        
        all_results.extend(successful)
        remaining_urls = [r['url'] for r in failed]
        
        print(f"本轮成功: {len(successful)}, 失败: {len(failed)}")
        
        if remaining_urls and attempt < max_retries:
            print("等待后重试...")
            import time
            time.sleep(10)  # 重试前等待
    
    return all_results

# 使用重试功能
final_results = extract_with_retry(urls)
print(f"最终成功: {len(final_results)} 个URL")
```

## 常见问题

### Q: 如何提高成功率？
A: 
- 减少并发数 (`max_workers=2`)
- 增加延迟 (`delay_between_batches=2.0`)
- 启用Selenium备用 (`use_selenium_fallback=True`)

### Q: 如何提高速度？
A: 
- 增加并发数 (`max_workers=5`)
- 减少延迟 (`delay_between_batches=0.5`)
- 对纯静态网站禁用Selenium (`use_selenium_fallback=False`)

### Q: 遇到大量429错误怎么办？
A: 
- 大幅减少并发数 (`max_workers=1`)
- 大幅增加延迟 (`delay_between_batches=5.0`)
- 考虑使用代理

### Q: 内存使用过高怎么办？
A: 
- 分批处理大量URL
- 减少并发数
- 及时处理和清理结果

## 测试命令

```bash
# 测试基本功能
python src/net_search/ddgs/selenium_utils.py test_concurrent

# 运行完整示例
python src/net_search/ddgs/concurrent_example.py
```

## 性能基准

基于测试结果：
- **单个处理**: 21.4秒 (3个URL)
- **并发处理**: 9.6秒 (3个URL)
- **性能提升**: 2.2倍

实际提升取决于：
- URL数量
- 网站响应速度
- 是否需要Selenium
- 网络条件
