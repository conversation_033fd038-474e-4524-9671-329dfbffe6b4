{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Token string: '>', Token ID: 29\n", "Token string: '>', Token ID: 397\n", "Token string: '>', Token ID: 861\n", "Token string: '>', Token ID: 1339\n", "Token string: '>', Token ID: 1433\n", "Token string: '></', Token ID: 1472\n", "Token string: '><', Token ID: 1784\n", "Token string: '>(', Token ID: 2235\n", "Token string: '>>', Token ID: 2452\n", "Token string: '>=', Token ID: 2604\n", "Token string: '>>', Token ID: 3578\n", "Token string: '>();', Token ID: 3913\n", "Token string: '>'', Token ID: 5592\n", "Token string: '>&', Token ID: 5789\n", "Token string: '>', Token ID: 6107\n", "Token string: '>\";', Token ID: 6734\n", "Token string: '>::', Token ID: 6831\n", "Token string: '>';', Token ID: 7165\n", "Token string: '>{', Token ID: 7309\n", "Token string: '><?', Token ID: 7508\n", "Token string: '>,', Token ID: 8066\n", "Token string: '>\\', Token ID: 8449\n", "Token string: '>)', Token ID: 9231\n", "Token string: '>\"', Token ID: 9877\n", "Token string: '>=', Token ID: 9922\n", "Token string: '>;', Token ID: 10133\n", "Token string: '>', Token ID: 10370\n", "Token string: '>', Token ID: 10389\n", "Token string: '>();', Token ID: 10448\n", "Token string: '>().', Token ID: 10483\n", "Token string: '>{{', Token ID: 11764\n", "Token string: '>>>', Token ID: 12109\n", "Token string: '>,', Token ID: 12520\n", "Token string: '>(\"', Token ID: 13211\n", "Token string: '><!--', Token ID: 13301\n", "Token string: '>()', Token ID: 13555\n", "Token string: '>.', Token ID: 14276\n", "Token string: '>\");', Token ID: 15084\n", "Token string: '>();', Token ID: 15921\n", "Token string: '>w', Token ID: 16300\n", "Token string: '>The', Token ID: 16357\n", "Token string: '>(', Token ID: 17055\n", "Token string: '>/', Token ID: 18105\n", "Token string: '>>>>', Token ID: 18153\n", "Token string: '>()', Token ID: 18949\n", "Token string: '>\"', Token ID: 19134\n", "Token string: '>;', Token ID: 19421\n", "Token string: '>*', Token ID: 19443\n", "Token string: '>',', Token ID: 19579\n", "Token string: '>\";', Token ID: 20078\n", "Token string: '>>>', Token ID: 20154\n", "Token string: '>\",', Token ID: 21156\n", "Token string: '>[', Token ID: 22071\n", "Token string: '>');', Token ID: 22085\n", "Token string: '>';', Token ID: 22857\n", "Token string: '>',', Token ID: 23174\n", "Token string: '>'', Token ID: 23431\n", "Token string: '>A', Token ID: 23465\n", "Token string: '>$', Token ID: 23586\n", "Token string: '>\\<', Token ID: 23635\n", "Token string: '>', Token ID: 25321\n", "Token string: '>>(', Token ID: 25526\n", "Token string: '>C', Token ID: 25630\n", "Token string: '>S', Token ID: 25837\n", "Token string: '>{{$', Token ID: 26076\n", "Token string: '></', Token ID: 26607\n", "Token string: '>manual', Token ID: 26667\n", "Token string: '>:', Token ID: 26818\n", "Token string: '>\\<^', Token ID: 27530\n", "Token string: '>', Token ID: 27701\n", "Token string: '><?=', Token ID: 28243\n", "Token string: '>x', Token ID: 28257\n", "Token string: '>T', Token ID: 28550\n", "Token string: '>%', Token ID: 28740\n", "Token string: '><', Token ID: 29348\n", "Token string: '>P', Token ID: 29478\n", "Token string: '>'.$', Token ID: 29541\n", "Token string: '>.', Token ID: 29816\n", "Token string: '>N', Token ID: 30010\n", "Token string: '>false', Token ID: 30392\n", "Token string: '>';', Token ID: 30463\n", "Token string: '>', Token ID: 30736\n", "Token string: '>[', Token ID: 30768\n", "Token string: '>}', Token ID: 31296\n", "Token string: '>\";', Token ID: 31923\n", "Token string: '>)', Token ID: 32279\n", "Token string: '>\";', Token ID: 32423\n", "Token string: '>());', Token ID: 32872\n", "Token string: '>>=', Token ID: 32971\n", "Token string: '>true', Token ID: 33284\n", "Token string: '>\".$', Token ID: 33643\n", "Token string: '>::', Token ID: 33813\n", "Token string: '>\",', Token ID: 35452\n", "Token string: '>-->', Token ID: 36360\n", "Token string: '>D', Token ID: 36495\n", "Token string: '>&', Token ID: 36498\n", "Token string: '>';', Token ID: 36709\n", "Token string: '>(', Token ID: 36720\n", "Token string: '>B', Token ID: 36721\n", "Token string: '>):', Token ID: 37023\n", "Token string: '>>();', Token ID: 37038\n", "Token string: '>M', Token ID: 37432\n", "Token string: '>${', Token ID: 37680\n", "Token string: '>equals', Token ID: 37706\n", "Token string: '>-', Token ID: 38643\n", "Token string: '>(),', Token ID: 39019\n", "Token string: '>>', Token ID: 39071\n", "Token string: '>', Token ID: 39206\n", "Token string: '>Main', Token ID: 39580\n", "Token string: '>')', Token ID: 39866\n", "Token string: '>.</', Token ID: 39874\n", "Token string: '>tag', Token ID: 40986\n", "Token string: '>No', Token ID: 41157\n", "Token string: '>s', Token ID: 41329\n", "Token string: '>{', Token ID: 41446\n", "Token string: '>'+', Token ID: 41909\n", "Token string: '>This', Token ID: 41993\n", "Token string: '>);', Token ID: 42013\n", "Token string: '>\")', Token ID: 42363\n", "Token string: '>--}}', Token ID: 43196\n", "Token string: '>\\', Token ID: 43373\n", "Token string: '>`', Token ID: 43626\n", "Token string: '>\");', Token ID: 43691\n", "Token string: '>a', Token ID: 43875\n", "Token string: '>R', Token ID: 43960\n", "Token string: '>'+', Token ID: 44501\n", "Token string: '>Name', Token ID: 44668\n", "Token string: '>'.', Token ID: 44689\n", "Token string: '>tagger', Token ID: 44694\n", "Token string: '>(&', Token ID: 44784\n", "Token string: '>(_', Token ID: 45609\n", "Token string: '>\"+', Token ID: 45957\n", "Token string: '>F', Token ID: 46460\n", "Token string: '>((', Token ID: 47453\n", "Token string: '>?', Token ID: 47585\n", "Token string: '>();', Token ID: 48281\n", "Token string: '>>>>>>>>', Token ID: 49413\n", "Token string: '>Email', Token ID: 50531\n", "Token string: '>V', Token ID: 50700\n", "Token string: '>b', Token ID: 51407\n", "Token string: '><?=$', Token ID: 51419\n", "Token string: '>@', Token ID: 51556\n", "Token string: '>();', Token ID: 51622\n", "Token string: '>}', Token ID: 52038\n", "Token string: '>n', Token ID: 52130\n", "Token string: '>//', Token ID: 52593\n", "Token string: '>alert', Token ID: 52777\n", "Token string: '>null', Token ID: 52872\n", "Token string: '>{@', Token ID: 52878\n", "Token string: '>\".', Token ID: 52946\n", "Token string: '>K', Token ID: 53300\n", "Token string: '>i', Token ID: 53364\n", "Token string: '>]', Token ID: 53529\n", "Token string: '>NN', Token ID: 53566\n", "Token string: '>`', Token ID: 53722\n", "Token string: '>Add', Token ID: 53745\n", "Token string: '>You', Token ID: 54521\n", "Token string: '>', Token ID: 54680\n", "Token string: '>}'', Token ID: 54722\n", "Token string: '>L', Token ID: 55600\n", "Token string: '>J', Token ID: 56325\n", "Token string: '>c', Token ID: 56346\n", "Token string: '>>', Token ID: 56948\n", "Token string: '>>', Token ID: 57545\n", "Token string: '>').', Token ID: 57576\n", "Token string: '>(()', Token ID: 58789\n", "Token string: '>>&', Token ID: 59267\n", "Token string: '>E', Token ID: 59384\n", "Token string: '>H', Token ID: 60500\n", "Token string: '>(*', Token ID: 60674\n", "Token string: '>t', Token ID: 60735\n", "Token string: '>())', Token ID: 61004\n", "Token string: '>\").', Token ID: 61032\n", "Token string: '>#', Token ID: 61125\n", "Token string: '>xpath', Token ID: 61222\n", "Token string: '>>,', Token ID: 61340\n", "Token string: '>/', Token ID: 61359\n", "Token string: '>v', Token ID: 61514\n", "Token string: '>`;', Token ID: 61563\n", "Token string: '>>()', Token ID: 61586\n", "Token string: '>Select', Token ID: 62405\n", "Token string: '>Total', Token ID: 63081\n", "Token string: '>Date', Token ID: 63311\n", "Token string: '>>>>>>>', Token ID: 63344\n", "Token string: '>Z', Token ID: 63416\n", "Token string: '>p', Token ID: 63627\n", "Token string: '>'.', Token ID: 63720\n", "Token string: '>In', Token ID: 65330\n", "Token string: '>(),', Token ID: 65766\n", "Token string: '>;', Token ID: 65795\n", "Token string: '>I', Token ID: 65828\n", "Token string: '>If', Token ID: 66053\n", "Token string: '>Error', Token ID: 66199\n", "Token string: '>Please', Token ID: 66304\n", "Token string: '>*/', Token ID: 66475\n", "Token string: '>('', Token ID: 67638\n", "Token string: '>/<', Token ID: 67772\n", "Token string: '>_', Token ID: 67911\n", "Token string: '><PERSON><PERSON>', Token ID: 67939\n", "Token string: '>\");', Token ID: 67940\n", "Token string: '>Description', Token ID: 69171\n", "Token string: '>|', Token ID: 69844\n", "Token string: '>{$', Token ID: 70046\n", "Token string: '>G', Token ID: 70294\n", "Token string: '>[]', Token ID: 70410\n", "Token string: '>d', Token ID: 71150\n", "Token string: '>).', Token ID: 71524\n", "Token string: '>k', Token ID: 72240\n", "Token string: '>An', Token ID: 72241\n", "Token string: '>,</', Token ID: 73198\n", "Token string: '><![', Token ID: 73199\n", "Token string: '>m', Token ID: 73803\n", "Token string: '>()', Token ID: 73953\n", "Token string: '>r', Token ID: 74408\n", "Token string: '>Status', Token ID: 75176\n", "Token string: '>Edit', Token ID: 75177\n", "Token string: '>Create', Token ID: 75608\n", "Token string: '>ID', Token ID: 75697\n", "Token string: '>', Token ID: 75719\n", "Token string: '>Your', Token ID: 76274\n", "Token string: '>X', Token ID: 76329\n", "Token string: '>New', Token ID: 77432\n", "Token string: '>:</', Token ID: 77567\n", "Token string: '>>::', Token ID: 77595\n", "Token string: '>}</', Token ID: 77935\n", "Token string: '>>;', Token ID: 77943\n", "Token string: '>O', Token ID: 78844\n", "Token string: '>');', Token ID: 78916\n", "Token string: '>>();', Token ID: 79279\n", "Token string: '>Password', Token ID: 79458\n", "Token string: '>Hello', Token ID: 79497\n", "Token string: '>e', Token ID: 79770\n", "Token string: '>')', Token ID: 79865\n", "Token string: '>Returns', Token ID: 80104\n", "Token string: '>>,', Token ID: 81360\n", "Token string: '>({', Token ID: 81483\n", "Token string: '>User', Token ID: 81766\n", "Token string: '>--', Token ID: 82157\n", "Token string: '>y', Token ID: 82244\n", "Token string: '>\")', Token ID: 82598\n", "Token string: '>\"', Token ID: 82606\n", "Token string: '>\",', Token ID: 82876\n", "Token string: '>\"+', Token ID: 83522\n", "Token string: '>;', Token ID: 84109\n", "Token string: '>f', Token ID: 84394\n", "Token string: '>Welcome', Token ID: 84668\n", "Token string: '>Title', Token ID: 85279\n", "Token string: '>Contact', Token ID: 85380\n", "Token string: '>>)', Token ID: 85602\n", "Token string: '><?', Token ID: 86152\n", "Token string: '>Login', Token ID: 86260\n", "Token string: '>Nama', Token ID: 86395\n", "Token string: '>/',', Token ID: 86864\n", "Token string: '>Type', Token ID: 87065\n", "Token string: '>');', Token ID: 87346\n", "Token string: '>Action', Token ID: 88010\n", "Token string: '>()->', Token ID: 88380\n", "Token string: '>window', Token ID: 88468\n", "Token string: '>{\"', Token ID: 88863\n", "Token string: '>About', Token ID: 89568\n", "Token string: '>{!!', Token ID: 89647\n", "Token string: '>this', Token ID: 89810\n", "Token string: '>Data', Token ID: 90121\n", "Token string: '>All', Token ID: 90247\n", "Token string: '>Show', Token ID: 91309\n", "Token string: '>List', Token ID: 92136\n", "Token string: '>())', Token ID: 92783\n", "Token string: '>To', Token ID: 93376\n", "Token string: '>j', Token ID: 93466\n", "Token string: '>());', Token ID: 93718\n", "Token string: '>We', Token ID: 94186\n", "Token string: '>.', Token ID: 94367\n", "Token string: '>+', Token ID: 95326\n", "Token string: '>'', Token ID: 95363\n", "Token string: '>Last', Token ID: 95376\n", "Token string: '>({', Token ID: 95753\n", "Token string: '>Delete', Token ID: 96204\n", "Token string: '>*</', Token ID: 96609\n", "Token string: '>>>(', Token ID: 96950\n", "Token string: '>');', Token ID: 97236\n", "Token string: '>>(', Token ID: 97238\n", "Token string: '>Loading', Token ID: 97460\n", "Token string: '>Note', Token ID: 97729\n", "Tokenization test completed. number of gt tokens:  283\n"]}], "source": ["import os\n", "from transformers import AutoTokenizer\n", "\n", "model_path = \"/home/<USER>/data/group/model_hub/huggingface/Qwen/Qwen2.5-3B-Instruct\"\n", "\n", "# 检查路径是否存在\n", "if not os.path.exists(model_path):\n", "    raise FileNotFoundError(f\"模型路径不存在: {model_path}\")\n", "\n", "# 加载 tokenizer，指定 local_files_only=True 防止尝试联网下载\n", "tokenizer = AutoTokenizer.from_pretrained(model_path, local_files_only=True)\n", "\n", "def find_gt_prefix_tokens(tokenizer, prefix='>'):\n", "    results = []\n", "    vocab_size = tokenizer.vocab_size if hasattr(tokenizer, 'vocab_size') else len(tokenizer)\n", "\n", "    for token_id in range(vocab_size):\n", "        token_str = tokenizer.decode([token_id], clean_up_tokenization_spaces=False).strip()\n", "        if token_str.startswith(prefix) :\n", "            results.append((token_str, token_id))\n", "    return results\n", "\n", "gt_tokens = find_gt_prefix_tokens(tokenizer, prefix='>')\n", "\n", "for token_str, token_id in gt_tokens:\n", "    print(f\"Token string: '{token_str}', Token ID: {token_id}\")\n", "print(\"Tokenization test completed. number of gt tokens: \", len(gt_tokens)) \n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}