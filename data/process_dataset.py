import os
import datasets
import jsonlines
import argparse
import random
random.seed(42)

import os
import sys

ROOT_PATH = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.append(ROOT_PATH)


def process_musique(local_dir='./'):
    musique_dir = os.path.join(local_dir, 'musique')

    # train_data_path = os.path.join(musique_dir, 'train.jsonl')
    # lines = []
    # with jsonlines.open(train_data_path) as reader:
    #     for line in reader:
    #         lines.append(line)
    # train_data = []
    # for line in lines:
    #     train_data.append({
    #         "data_source": "musique",
    #         "question": line['question'],
    #         "ability": "qa",
    #         "reward_model": {
    #                 "style": "rule",
    #                 "ground_truth": line['golden_answers']
    #             },
    #         "extra_info": {
    #             "id": line['id'],
    #         }
    #     })
    # train_dataset = datasets.Dataset.from_list(train_data)
    # train_dataset.to_parquet(os.path.join(musique_dir, 'train.parquet'))


    dev_data_path = os.path.join(musique_dir, 'dev.jsonl')
    lines = []
    with jsonlines.open(dev_data_path) as reader:
        for line in reader:
            lines.append(line)
    dev_data = []
    random.shuffle(lines)
    for line in lines[:]:
        dev_data.append({
            "data_source": "musique",
            "question": line['question'],
            "ability": "qa",
            "reward_model": {
                "style": "rule",
                "ground_truth": line['golden_answers']
            },
            "extra_info": {
                "id": line['id'],
            }
        })
    test_dataset = datasets.Dataset.from_list(dev_data)
    test_dataset.to_parquet(os.path.join(musique_dir, 'val.parquet'))


def process_hotpotqa(local_dir='./'):
    hotpotqa_dir = os.path.join(local_dir, 'hotpotqa')

    # train_data_path = os.path.join(hotpotqa_dir, 'train.jsonl')
    # lines = []
    # with jsonlines.open(train_data_path) as reader:
    #     for line in reader:
    #         lines.append(line)
    # train_data = []
    # for line in lines:
    #     train_data.append({
    #         "data_source": "hotpotqa",
    #         "question": line['question'],
    #         "ability": "qa",
    #         "reward_model": {
    #                 "style": "rule",
    #                 "ground_truth": line['golden_answers']
    #             },
    #         "extra_info": {
    #             "id": line['id'],
    #         }
    #     })
    # train_dataset = datasets.Dataset.from_list(train_data)
    # train_dataset.to_parquet(os.path.join(hotpotqa_dir, 'train.parquet'))

    dev_data_path = os.path.join(hotpotqa_dir, 'dev.jsonl')
    lines = []
    with jsonlines.open(dev_data_path) as reader:
        for line in reader:
            lines.append(line)
    dev_data = []
    random.shuffle(lines)
    for line in lines[:]:
        dev_data.append({
            "data_source": "hotpotqa",
            "question": line['question'],
            "ability": "qa",
            "reward_model": {
                "style": "rule",
                "ground_truth": line['golden_answers']
            },
            "extra_info": {
                "id": line['id'],
            }
        })
    test_dataset = datasets.Dataset.from_list(dev_data)
    test_dataset.to_parquet(os.path.join(hotpotqa_dir, 'val.parquet'))


def process_bamboogle(local_dir='./', number=0):
    bamboogle_dir = os.path.join(local_dir, 'bamboogle')

    dev_data_path = os.path.join(bamboogle_dir, 'test.jsonl')
    lines = []
    with jsonlines.open(dev_data_path) as reader:
        for line in reader:
            lines.append(line)
    dev_data = []
    for line in lines[:number if number > 0 else len(lines)]:
        dev_data.append({
            "data_source": "bamboogle",
            "question": line['question'],
            "ability": "qa",
            "reward_model": {
                "style": "rule",
                "ground_truth": line['golden_answers']
            },
            "extra_info": {
                "id": line['id'],
            }
        })

    test_dataset = datasets.Dataset.from_list(dev_data)
    test_dataset.to_parquet(os.path.join(bamboogle_dir, f'val{number if number > 0 else ""}.parquet'))


def process_2wikimultihopqa(local_dir='./'):
    wikimultihopqa_dir = os.path.join(local_dir, '2wikimultihopqa')

    # train_data_path = os.path.join(wikimultihopqa_dir, 'train.jsonl')
    # lines = []
    # with jsonlines.open(train_data_path) as reader:
    #     for line in reader:
    #         lines.append(line)
    # train_data = []
    # for line in lines:
    #     train_data.append({
    #         "data_source": "2wikimultihopqa",
    #         "question": line['question'],
    #         "ability": "qa",
    #         "reward_model": {
    #                 "style": "rule",
    #                 "ground_truth": line['golden_answers']
    #             },
    #         "extra_info": {
    #             "id": line['id'],
    #         }
    #     })
    # train_dataset = datasets.Dataset.from_list(train_data)
    # train_dataset.to_parquet(os.path.join(wikimultihopqa_dir, 'train.parquet'))

    dev_data_path = os.path.join(wikimultihopqa_dir, 'dev.jsonl')
    lines = []
    with jsonlines.open(dev_data_path) as reader:
        for line in reader:
            lines.append(line)
    dev_data = []
    for line in lines:
        dev_data.append({
            "data_source": "2wikimultihopqa",
            "question": line['question'],
            "ability": "qa",
            "reward_model": {
                "style": "rule",
                "ground_truth": line['golden_answers']
            },
            "extra_info": {
                "id": line['id'],
            }
        })
    test_dataset = datasets.Dataset.from_list(dev_data)
    test_dataset.to_parquet(os.path.join(wikimultihopqa_dir, 'val.parquet'))


if __name__ == '__main__':
    local_dir = "/home/<USER>/code/personal/S9058924/SRR1/data"
    # process_2wikimultihopqa(local_dir)
    process_bamboogle(local_dir, number=10)
    # process_hotpotqa(local_dir)
    # process_musique(local_dir)