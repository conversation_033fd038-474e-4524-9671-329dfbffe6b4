# Configuration

FULLPAGE_SPLIT_DICT = {
    'slice_height': 512,
    'max_slices': 10
}

MAX_QUERY_LENGTH = 300
USER_AGENT = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36' 
IMAGE_SEARCH_RESULT = 10
DEFAULT_IMAGE_TOKEN = '<image>'
SAVE_IMAGE_DIR = 'vlmeval_model_image_dir' # used to save input images to model implemented vlmevalkit
# Time for loading the website
BRIEF_TIMEOUT = 5000 # ms. Should be changed according to the network speed
FULLPAGE_TIMEOUT = 2500 # ms. Should be changed according to the network speed
FULLPAGE_CONTENT_TIMEOUT = 5000 # ms. Should be changed according to the network speed

# PROXY = "http://nbproxy.mlp.oppo.local:8888"
