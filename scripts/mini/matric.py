import torch
import time
from datetime import datetime
import pynvml

# 初始化 NVML（NVIDIA Management Library）用于监测 GPU 使用率
pynvml.nvmlInit()

def get_gpu_utilization(gpu_index=0):
    handle = pynvml.nvmlDeviceGetHandleByIndex(gpu_index)
    util = pynvml.nvmlDeviceGetUtilizationRates(handle)
    return util.gpu  # 返回GPU使用率百分比

def big_matrix_multiplication(device_id=0, size=20000):
    # 在指定的GPU上进行大矩阵乘法，size根据实际显存调整
    device = torch.device(f'cuda:{device_id}')
    print(f"[{datetime.now()}] Starting matrix multiplication on GPU {device_id} with size {size}x{size}")

    # 随机生成两个大矩阵
    A = torch.randn(size, size, device=device)
    B = torch.randn(size, size, device=device)

    # 乘法运算
    C = torch.matmul(A, B)

    # 简单操作确保结果被使用，防止被优化
    result_sum = C.sum().item()
    print(f"[{datetime.now()}] Completed multiplication on GPU {device_id}, sum={result_sum}")
    return result_sum


def main_loop(interval_seconds=3600, matrix_size=20000):
    """
    每隔 interval_seconds 秒运行一次矩阵乘法，保持 GPU 占用。
    """
    num_gpus = torch.cuda.device_count()
    print(f"Detected {num_gpus} GPUs.")

    while True:
        start_time = time.time()

        # 对每个GPU执行矩阵乘法
        for gpu_id in range(min(num_gpus, 2)):  # 只用两块卡
            for i in range(3):
                big_matrix_multiplication(device_id=gpu_id, size=matrix_size)

            # 查询并打印当前GPU 利用率
            util = get_gpu_utilization(gpu_id)
            print(f"GPU {gpu_id} Utilization: {util}%")

            # 如果低于10%，可以考虑增加矩阵大小或增加运算次数（这里仅打印提醒）
            if util < 10:
                print(f"Warning: GPU {gpu_id} utilization below 10%! Consider increasing workload.")

        # 计算剩余等待时间
        elapsed = time.time() - start_time
        sleep_time = max(0, interval_seconds - elapsed)
        print(f"Sleeping for {sleep_time:.1f} seconds until next run...\n")
        time.sleep(sleep_time)


if __name__ == "__main__":
    # 调整矩阵大小，比如 20000 x 20000 需根据显存大小调整
    # A100 80G 理论上能支持较大矩阵，但要留出余量避免 OOM
    main_loop(interval_seconds=3600, matrix_size=18000)
