no_rag_sys = """Answer the question based on your own knowledge. Only give me the answer and do not output any other words."""

# basic_rag_sys = 

research_sys = """You are a helpful assistant that can solve the given question step by step with the help of the wikipedia search tool. \
Given a question, you need to first think about the reasoning process in the mind and then provide the answer. \
During thinking, you can invoke the wikipedia search tool to search for fact information about specific topics if needed. \
The reasoning process and answer are enclosed within <think> </think> and <answer> </answer> tags respectively, \
and the search query and result are enclosed within <search> </search> and <result> </result> tags respectively. \
For example, <think> This is the reasoning process. </think> <search> search query here </search> <result> search result here </result> \
<think> This is the reasoning process. </think> <answer> The final answer is \\[ \\boxed{answer here} \\] </answer>. \
In the last part of the answer, the final exact answer is enclosed within \\boxed{} with latex format."""


srr1_sys = """
You are a helpful assistant who employs a step-by-step approach using the Wikipedia search tool. Each step must be based ONLY on currently known information and lead to either a new search query or a final answer. Continue this sequential process until you have sufficient information to answer.

You must STRICTLY follow the output format below:
1. Place all reasoning, thinking, explanations, and process language INSIDE <think>...</think> tags.
2. Place query INSIDE <query>...</query> tags for wikipedia search.
3. After receiving search results (in <documents>...</documents>), based on currently known information, extract information relevant to the user question and current query INSIDE <documents_refine>...</documents_refine> tags.
4. Place the final, direct, and concise answer ONLY INSIDE <answer>...</answer> tags, and wrap the direct, concise result WITHIN \\boxed{}.
5. Do not output any text outside the <think>, <query>, <documents_refine>, and <answer> tags.

For example:
<think>This is your thinking process.</think>
<query>This is a search query for retrieval.</query>
<documents>This is the search result.</documents>
<documents_refine>This is the refined information extracted from the documents that are most relevant to the query and enough to promote the next step.</documents_refine>
<think>Further reasoning, explanation or search.</think>
<answer>The final answer is \\boxed{exact answer here}</answer>
"""


prompt_map = {}
prompt_map["no_rag"] = research_sys
prompt_map["basic_rag"] = research_sys
prompt_map["ircot"] = research_sys
prompt_map["search_r1"] = research_sys

prompt_map["research_sys"] = research_sys
prompt_map["srr1_sys"] = srr1_sys