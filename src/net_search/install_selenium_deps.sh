#!/bin/bash

echo "安装Selenium、DuckDuck GO 相关依赖..."

# 安装Python包
pip install selenium webdriver-manager
pip install duckduckgo_search

# 检查Chrome是否已安装
if command -v google-chrome &> /dev/null; then
    echo "✅ Chrome已安装"
else
    echo "❌ Chrome未安装，正在安装..."
    
    # 更新包列表
    sudo apt-get update
    
    # 安装Chrome
    wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
    sudo sh -c 'echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google-chrome.list'
    sudo apt-get update
    sudo apt-get install -y google-chrome-stable
fi

echo "✅ 安装完成！"
echo "现在可以使用selenium_utils.py处理需要JavaScript的网站了"
