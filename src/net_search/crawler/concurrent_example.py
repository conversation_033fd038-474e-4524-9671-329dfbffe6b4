#!/usr/bin/env python3
"""
并发网页内容提取示例
"""

from selenium_utils import extract_text_concurrent, extract_text_hybrid

def simple_progress_callback(completed, total, result):
    """简单的进度显示"""
    percentage = (completed / total) * 100
    status = "✅" if result['success'] else "❌"
    print(f"进度: {percentage:5.1f}% ({completed}/{total}) {status} {result['url']}")

def example_basic_concurrent():
    """基础并发提取示例"""
    print("📚 示例1: 基础并发提取")
    print("="*50)
    
    urls = [
        'https://en.wikipedia.org/wiki/Python_(programming_language)',
        'https://en.wikipedia.org/wiki/JavaScript',
        'https://en.wikipedia.org/wiki/Machine_learning',
        'https://en.wikipedia.org/wiki/Artificial_intelligence',
    ]
    
    # 并发提取，3个线程
    results = extract_text_concurrent(
        urls=urls,
        max_workers=3,
        use_selenium_fallback=True,
        progress_callback=simple_progress_callback
    )
    
    # 处理结果
    successful_results = [r for r in results if r['success']]
    print(f"\n✅ 成功提取 {len(successful_results)} 个网页")
    
    return results

def example_custom_settings():
    """自定义设置示例"""
    print("\n🔧 示例2: 自定义设置")
    print("="*50)
    
    urls = [
        'https://www.bookbrowse.com/biographies/index.cfm/author_number/1687/Greg-Rucka',
        'https://m.imdb.com/name/nm1421638/',
        'https://en.wikipedia.org/wiki/Greg_Rucka',
    ]
    
    def detailed_callback(completed, total, result):
        method_info = f" [{result['method']}]" if result['method'] else ""
        content_info = f" ({len(result['content'])} chars)" if result['content'] else ""
        error_info = f" - {result['error']}" if result['error'] else ""
        
        status = "✅" if result['success'] else "❌"
        print(f"  {completed}/{total}: {status}{method_info}{content_info} {result['url']}{error_info}")
    
    # 更保守的设置：更少并发，更长延迟
    results = extract_text_concurrent(
        urls=urls,
        max_workers=2,  # 减少并发数
        use_selenium_fallback=True,
        progress_callback=detailed_callback,
        delay_between_batches=2.0  # 增加延迟
    )
    
    return results

def example_batch_processing():
    """批量处理示例"""
    print("\n📦 示例3: 批量处理大量URL")
    print("="*50)
    
    # 模拟大量URL
    base_urls = [
        'https://en.wikipedia.org/wiki/Python_(programming_language)',
        'https://en.wikipedia.org/wiki/JavaScript',
        'https://en.wikipedia.org/wiki/Java_(programming_language)',
        'https://en.wikipedia.org/wiki/C%2B%2B',
        'https://en.wikipedia.org/wiki/Go_(programming_language)',
        'https://en.wikipedia.org/wiki/Rust_(programming_language)',
        'https://en.wikipedia.org/wiki/TypeScript',
        'https://en.wikipedia.org/wiki/Swift_(programming_language)',
    ]
    
    def batch_callback(completed, total, result):
        if completed % 2 == 0 or completed == total:  # 每2个显示一次进度
            percentage = (completed / total) * 100
            print(f"📊 批量处理进度: {percentage:5.1f}% ({completed}/{total})")
    
    # 批量处理
    results = extract_text_concurrent(
        urls=base_urls,
        max_workers=4,  # 稍微增加并发
        use_selenium_fallback=False,  # 只用requests，更快
        progress_callback=batch_callback,
        delay_between_batches=0.5
    )
    
    # 统计结果
    success_count = sum(1 for r in results if r['success'])
    total_chars = sum(len(r['content']) for r in results if r['content'])
    
    print(f"\n📈 批量处理完成:")
    print(f"   成功: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
    print(f"   总字符数: {total_chars:,}")
    print(f"   平均字符数: {total_chars//success_count if success_count > 0 else 0:,}")
    
    return results

def example_single_vs_concurrent():
    """单个vs并发性能对比"""
    print("\n⚡ 示例4: 性能对比")
    print("="*50)
    
    urls = [
        'https://en.wikipedia.org/wiki/Python_(programming_language)',
        'https://en.wikipedia.org/wiki/JavaScript',
        'https://en.wikipedia.org/wiki/Machine_learning',
    ]
    
    # 单个处理
    print("🐌 单个处理...")
    import time
    start_time = time.time()
    
    single_results = []
    for url in urls:
        result = extract_text_hybrid(url)
        single_results.append({
            'url': url,
            'content': result,
            'success': result is not None
        })
    
    single_time = time.time() - start_time
    
    # 并发处理
    print("\n🚀 并发处理...")
    start_time = time.time()
    
    concurrent_results = extract_text_concurrent(
        urls=urls,
        max_workers=3,
        use_selenium_fallback=True,
        progress_callback=None,  # 不显示详细进度
        delay_between_batches=0.5
    )
    
    concurrent_time = time.time() - start_time
    
    # 对比结果
    print(f"\n📊 性能对比:")
    print(f"   单个处理: {single_time:.1f} 秒")
    print(f"   并发处理: {concurrent_time:.1f} 秒")
    print(f"   提升倍数: {single_time/concurrent_time:.1f}x")
    
    return single_results, concurrent_results

if __name__ == '__main__':
    print("🌐 并发网页内容提取示例")
    print("="*60)
    
    # 运行所有示例
    try:
        example_basic_concurrent()
        example_custom_settings()
        example_batch_processing()
        example_single_vs_concurrent()
        
        print("\n🎉 所有示例运行完成！")
        
    except KeyboardInterrupt:
        print("\n⏹️  用户中断")
    except Exception as e:
        print(f"\n❌ 运行错误: {e}")
        import traceback
        traceback.print_exc()
