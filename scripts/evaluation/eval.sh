#!/usr/bin/env bash
cd /home/<USER>/code/personal/S9058924/SRR1/scripts/evaluation

# Force vLLM to use spawn method for multiprocessing
export PYTHONUNBUFFERED=1
export VLLM_USE_MULTIPROCESSING_SPAWN=1

export PYTHONPATH="/home/<USER>/code/personal/S9058924/SRR1/src:$PYTHONPATH"

python run_eval.py \
    --config_path eval_config.yaml \
    --method_name srr1 \
    --data_dir /home/<USER>/data/group/AI_Search/ReSearch/data/ \
    --dataset_name musique \
    --split dev \
    --save_dir /home/<USER>/data/personal/S9058924/SRR1/eval \
    --save_note 3B-org \
    --sgl_remote_url "http://0.0.0.0:8000" \
    --remote_retriever_url "http://*************:8000" \
    --generator_model /home/<USER>/data/group/model_hub/huggingface/Qwen/Qwen2.5-3B-Instruct
