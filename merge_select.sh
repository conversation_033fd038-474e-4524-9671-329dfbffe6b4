# 定义多个基础目录数组
BASE_DIRS=(
    "/home/<USER>/data/personal/S9058924/SRR1/output/Qwen-7B-high_kl_loss_coef-250613155312/global_step_154"
    "/home/<USER>/data/personal/S9058924/SRR1/output/Qwen-7B-high_kl_loss_coef-250613155312/global_step_100"
    "/home/<USER>/data/personal/S9058924/SRR1/output/Qwen-7B-high_kl_coef-250613155051/global_step_100"
    "/home/<USER>/data/personal/S9058924/SRR1/output/Qwen-7B-high_kl_coef-250613155051/global_step_154"
    "/home/<USER>/data/personal/S9058924/SRR1/output/7B-low_lr-250613170108/global_step_100"
    "/home/<USER>/data/personal/S9058924/SRR1/output/7B-low_lr-250613170108/global_step_154"
    "/home/<USER>/data/personal/S9058924/SRR1/output/7B-high5-kl-loss-250618103613/global_step_100"
    "/home/<USER>/data/personal/S9058924/SRR1/output/7B-high5-kl-loss-250618103613/global_step_200"
    "/home/<USER>/data/personal/S9058924/SRR1/output/7B-high2-both-kl-250618113341/global_step_100"
    "/home/<USER>/data/personal/S9058924/SRR1/output/7B-high2-both-kl-250618113341/global_step_200"
    "/home/<USER>/data/personal/S9058924/SRR1/output/7B-high_both_kl_coef-250613155522/global_step_100"
    "/home/<USER>/data/personal/S9058924/SRR1/output/7B-high_both_kl_coef-250613155522/global_step_154"
    "/home/<USER>/data/personal/S9058924/SRR1/output/7B-epoch10-250617161543/global_step_100"
    "/home/<USER>/data/personal/S9058924/SRR1/output/7B-epoch10-250617161543/global_step_200"
    "/home/<USER>/data/personal/S9058924/SRR1/output/7B-epoch10-250617161543/global_step_300"
)

# 遍历所有基础目录
for BASE_DIR in "${BASE_DIRS[@]}"; do
    echo "Processing directory: ${BASE_DIR}"
    
    python model_merge.py \
        --backend fsdp \
        --hf_model_path "${BASE_DIR}/actor/huggingface" \
        --local_dir "${BASE_DIR}/actor" \
        --target_dir "${BASE_DIR}/actor/huggingface"
        
    if [ $? -ne 0 ]; then
        echo "Error occurred while processing ${BASE_DIR}"
        # 可以选择继续处理下一个或退出
        # exit 1
    else
        # 成功执行后，清理actor目录
        echo "Cleaning up ${BASE_DIR}/actor..."
        find "${BASE_DIR}/actor" -mindepth 1 -maxdepth 1 -not -name "huggingface" -exec rm -rf {} +
    fi
done